import {initializeApp} from "firebase-admin/app";
import {setGlobalOptions} from "firebase-functions";

// OPTIMIZED global options for minimal resource usage
setGlobalOptions({
  maxInstances: 3,           // Reduced from 10 to 3
  region: 'us-central1',
  memory: '256MiB',          // Reduced from 512MiB to 256MiB (50% reduction)
  timeoutSeconds: 30,        // Reduced from 60 to 30 seconds (50% reduction)
  minInstances: 0,           // Ensure cold starts to save resources
});

// Initialize Firebase Admin
initializeApp();

// ===== CORE MESSAGING FUNCTIONS (ALREADY DEPLOYED) =====
export {
  onMessageCreateV2,
  markMessagesAsReadV2,
  createChatV2,
} from './messaging-v2';

// ===== CORE CALLING FUNCTIONS (ALREADY DEPLOYED) =====
export {
  initiateCallV2,
  answerCallV2,
  rejectCallV2,
  endCallV2,
  updateCallSignalingV2,
} from './calls-v2';

// ===== NOTIFICATION FUNCTIONS (BATCH 2) =====
export {
  onFollowCreateV2,
  onFollowDeleteV2,
  // onLikeCreateV2, // Skip this one due to previous quota issues
  onLikeDeleteV2,
  onCommentCreateV2,
  onShareCreateV2,
} from './notifications-v2';

console.log('🚀 Billionaires Social Backend v2 (BATCH 2 - Core + Notifications) initialized!');
console.log('📱 Available functions:');
console.log('  💬 Messaging: onMessageCreateV2, markMessagesAsReadV2, createChatV2');
console.log('  📞 Calling: initiateCallV2, answerCallV2, rejectCallV2, endCallV2, updateCallSignalingV2');
console.log('  🔔 Notifications: onFollowCreateV2, onFollowDeleteV2, onLikeDeleteV2, onCommentCreateV2, onShareCreateV2');
console.log('  ⚡ Optimized: 256MiB memory, 30s timeout, 3 max instances');
console.log('  📊 Total Functions: 13 (Core + Notifications)');
