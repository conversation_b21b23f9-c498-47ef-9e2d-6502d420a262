import {onDocumentCreated} from 'firebase-functions/v2/firestore';
import {on<PERSON>all, HttpsError} from 'firebase-functions/v2/https';
import {getFirestore, FieldValue} from 'firebase-admin/firestore';
import {logger} from 'firebase-functions';

const db = getFirestore();

/**
 * Moderate content when a new post is created
 */
export const moderatePostV2 = onDocumentCreated('posts/{postId}', async (event) => {
  const snap = event.data;
  if (!snap) return;

  const postId = event.params.postId;
  const post = snap.data();

  logger.info(`🛡️ Moderating post ${postId}`);

  try {
    const moderationResult = await analyzeContent({
      text: post.content || '',
      imageUrl: post.imageUrl || '',
      videoUrl: post.videoUrl || '',
      userId: post.userId,
      type: 'post',
    });

    // Update post with moderation results
    await snap.ref.update({
      moderation: {
        status: moderationResult.status,
        score: moderationResult.score,
        flags: moderationResult.flags,
        moderatedAt: FieldValue.serverTimestamp(),
      },
      isVisible: moderationResult.status === 'approved',
    });

    // If content is flagged, create moderation alert
    if (moderationResult.status === 'flagged') {
      await createModerationAlert(postId, 'post', post.userId, moderationResult);
    }

    logger.info(`✅ Post ${postId} moderated: ${moderationResult.status}`);
  } catch (error) {
    logger.error('Error moderating post:', error);
  }
});

/**
 * Moderate content when a new comment is created
 */
export const moderateCommentV2 = onDocumentCreated('posts/{postId}/comments/{commentId}', async (event) => {
  const snap = event.data;
  if (!snap) return;

  const commentId = event.params.commentId;
  const postId = event.params.postId;
  const comment = snap.data();

  logger.info(`🛡️ Moderating comment ${commentId}`);

  try {
    const moderationResult = await analyzeContent({
      text: comment.content || '',
      userId: comment.userId,
      type: 'comment',
      parentId: postId,
    });

    // Update comment with moderation results
    await snap.ref.update({
      moderation: {
        status: moderationResult.status,
        score: moderationResult.score,
        flags: moderationResult.flags,
        moderatedAt: FieldValue.serverTimestamp(),
      },
      isVisible: moderationResult.status === 'approved',
    });

    // If content is flagged, create moderation alert
    if (moderationResult.status === 'flagged') {
      await createModerationAlert(commentId, 'comment', comment.userId, moderationResult);
    }

    logger.info(`✅ Comment ${commentId} moderated: ${moderationResult.status}`);
  } catch (error) {
    logger.error('Error moderating comment:', error);
  }
});

/**
 * Handle user reports
 */
export const handleReportV2 = onDocumentCreated('reports/{reportId}', async (event) => {
  const snap = event.data;
  if (!snap) return;

  const reportId = event.params.reportId;
  const report = snap.data();

  logger.info(`🚨 Processing report ${reportId}`);

  try {
    // Get the reported content
    let contentDoc;
    if (report.contentType === 'post') {
      contentDoc = await db.collection('posts').doc(report.contentId).get();
    } else if (report.contentType === 'comment') {
      const [postId, commentId] = report.contentId.split('/');
      contentDoc = await db.collection('posts').doc(postId).collection('comments').doc(commentId).get();
    } else if (report.contentType === 'user') {
      contentDoc = await db.collection('users').doc(report.contentId).get();
    }

    if (!contentDoc || !contentDoc.exists) {
      logger.log(`Reported content ${report.contentId} not found`);
      return;
    }

    // Check if this content has been reported multiple times
    const existingReports = await db.collection('reports')
      .where('contentId', '==', report.contentId)
      .where('contentType', '==', report.contentType)
      .where('status', '==', 'pending')
      .get();

    const reportCount = existingReports.size;
    logger.info(`Content ${report.contentId} has ${reportCount} reports`);

    // Auto-moderate if report threshold is reached
    if (reportCount >= 3) {
      await autoModerateContent(report.contentId, report.contentType, reportCount);
    }

    // Create moderation alert for manual review
    await createModerationAlert(report.contentId, report.contentType, report.reportedUserId, {
      status: 'reported',
      reportCount,
      reportReasons: existingReports.docs.map(doc => doc.data().reason),
      flags: ['user_reported'],
    });

    logger.info(`✅ Report ${reportId} processed`);
  } catch (error) {
    logger.error('Error handling report:', error);
  }
});

/**
 * Cloud function to resolve moderation alerts
 */
export const resolveModerationAlertV2 = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const data = request.data;
  const { alertId, action, reason } = data; // action: 'approve' | 'reject' | 'warn'
  const moderatorId = request.auth.uid;

  try {
    // Verify moderator permissions
    const moderatorDoc = await db.collection('users').doc(moderatorId).get();
    if (!moderatorDoc.exists || !moderatorDoc.data()?.isModerator) {
      throw new HttpsError('permission-denied', 'Insufficient permissions');
    }

    // Get alert
    const alertDoc = await db.collection('moderation_alerts').doc(alertId).get();
    if (!alertDoc.exists) {
      throw new HttpsError('not-found', 'Alert not found');
    }

    const alertData = alertDoc.data()!;

    // Update alert status
    await alertDoc.ref.update({
      status: 'resolved',
      action,
      reason,
      moderatorId,
      resolvedAt: FieldValue.serverTimestamp(),
    });

    // Apply moderation action
    await applyModerationAction(alertData.contentId, alertData.contentType, alertData.userId, action, reason);

    return { success: true };
  } catch (error) {
    logger.error('Error resolving moderation alert:', error);
    throw error;
  }
});

/**
 * Auto-moderate content based on reports
 */
async function autoModerateContent(contentId: string, contentType: string, reportCount: number) {
  try {
    logger.info(`🤖 Auto-moderating ${contentType} ${contentId} (${reportCount} reports)`);

    let contentRef;
    if (contentType === 'post') {
      contentRef = db.collection('posts').doc(contentId);
    } else if (contentType === 'comment') {
      const [postId, commentId] = contentId.split('/');
      contentRef = db.collection('posts').doc(postId).collection('comments').doc(commentId);
    } else if (contentType === 'user') {
      contentRef = db.collection('users').doc(contentId);
    }

    if (!contentRef) return;

    // Hide content temporarily
    await contentRef.update({
      isVisible: false,
      autoModerated: true,
      autoModerationReason: `Hidden due to ${reportCount} user reports`,
      autoModeratedAt: FieldValue.serverTimestamp(),
    });

    // If it's a user, suspend their account temporarily
    if (contentType === 'user') {
      await contentRef.update({
        isSuspended: true,
        suspensionReason: `Account suspended due to ${reportCount} user reports`,
        suspendedAt: FieldValue.serverTimestamp(),
        suspensionExpiry: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      });
    }

    logger.info(`✅ Auto-moderated ${contentType} ${contentId}`);
  } catch (error) {
    logger.error('Error in auto-moderation:', error);
  }
}

/**
 * Analyze content for moderation
 */
async function analyzeContent(content: {
  text: string;
  imageUrl?: string;
  videoUrl?: string;
  userId: string;
  type: string;
  parentId?: string;
}): Promise<{
  status: 'approved' | 'flagged' | 'rejected';
  score: number;
  flags: string[];
}> {
  const flags: string[] = [];
  let score = 0;

  // Text analysis
  if (content.text) {
    const textAnalysis = analyzeText(content.text);
    score += textAnalysis.score;
    flags.push(...textAnalysis.flags);
  }

  // Check user history
  const userHistory = await getUserModerationHistory(content.userId);
  if (userHistory.violationCount > 3) {
    score += 30;
    flags.push('repeat_offender');
  }

  // Determine status based on score
  let status: 'approved' | 'flagged' | 'rejected';
  if (score >= 80) {
    status = 'rejected';
  } else if (score >= 40) {
    status = 'flagged';
  } else {
    status = 'approved';
  }

  return { status, score, flags };
}

/**
 * Analyze text content for inappropriate content
 */
function analyzeText(text: string): { score: number; flags: string[] } {
  const flags: string[] = [];
  let score = 0;

  // Profanity detection (basic implementation)
  const profanityWords = [
    'spam', 'scam', 'fake', 'hate', 'violence', 'harassment',
    // Add more words as needed
  ];

  const lowerText = text.toLowerCase();
  for (const word of profanityWords) {
    if (lowerText.includes(word)) {
      score += 20;
      flags.push('inappropriate_language');
      break;
    }
  }

  // Spam detection
  if (text.length > 1000) {
    score += 10;
    flags.push('excessive_length');
  }

  // URL spam detection
  const urlCount = (text.match(/https?:\/\/[^\s]+/g) || []).length;
  if (urlCount > 2) {
    score += 25;
    flags.push('excessive_links');
  }

  // Repeated characters (spam indicator)
  if (/(.)\1{4,}/.test(text)) {
    score += 15;
    flags.push('repeated_characters');
  }

  // All caps (shouting)
  if (text.length > 20 && text === text.toUpperCase()) {
    score += 10;
    flags.push('all_caps');
  }

  return { score, flags };
}

/**
 * Get user's moderation history
 */
async function getUserModerationHistory(userId: string): Promise<{
  violationCount: number;
  lastViolation?: any;
}> {
  try {
    const violations = await db.collection('moderation_violations')
      .where('userId', '==', userId)
      .orderBy('createdAt', 'desc')
      .limit(10)
      .get();

    return {
      violationCount: violations.size,
      lastViolation: violations.empty ? undefined : violations.docs[0].data().createdAt,
    };
  } catch (error) {
    logger.error('Error getting user moderation history:', error);
    return { violationCount: 0 };
  }
}

/**
 * Create moderation alert for manual review
 */
async function createModerationAlert(
  contentId: string,
  contentType: string,
  userId: string,
  moderationResult: any
) {
  try {
    await db.collection('moderation_alerts').add({
      contentId,
      contentType,
      userId,
      status: 'pending',
      priority: moderationResult.score >= 60 ? 'high' : 'medium',
      flags: moderationResult.flags,
      score: moderationResult.score,
      reportCount: moderationResult.reportCount || 0,
      createdAt: FieldValue.serverTimestamp(),
    });

    logger.info(`🚨 Moderation alert created for ${contentType} ${contentId}`);
  } catch (error) {
    logger.error('Error creating moderation alert:', error);
  }
}

/**
 * Apply moderation action to content
 */
async function applyModerationAction(
  contentId: string,
  contentType: string,
  userId: string,
  action: string,
  reason: string
) {
  try {
    if (action === 'reject') {
      // Hide/delete content
      let contentRef;
      if (contentType === 'post') {
        contentRef = db.collection('posts').doc(contentId);
      } else if (contentType === 'comment') {
        const [postId, commentId] = contentId.split('/');
        contentRef = db.collection('posts').doc(postId).collection('comments').doc(commentId);
      }

      if (contentRef) {
        await contentRef.update({
          isVisible: false,
          moderationAction: 'rejected',
          moderationReason: reason,
          moderatedAt: FieldValue.serverTimestamp(),
        });
      }

      // Record violation
      await db.collection('moderation_violations').add({
        userId,
        contentId,
        contentType,
        action: 'rejected',
        reason,
        createdAt: FieldValue.serverTimestamp(),
      });

    } else if (action === 'warn') {
      // Send warning to user
      await db.collection('user_warnings').add({
        userId,
        contentId,
        contentType,
        reason,
        createdAt: FieldValue.serverTimestamp(),
      });

      // Send notification to user
      await db.collection('notification_requests').add({
        userId,
        type: 'warning',
        title: 'Content Warning',
        body: `Your ${contentType} has been flagged: ${reason}`,
        data: { contentId, contentType, reason },
        createdAt: FieldValue.serverTimestamp(),
      });
    }

    logger.info(`✅ Applied moderation action: ${action} for ${contentType} ${contentId}`);
  } catch (error) {
    logger.error('Error applying moderation action:', error);
  }
}
