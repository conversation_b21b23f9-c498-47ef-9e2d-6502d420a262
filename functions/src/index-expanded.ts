import {initializeApp} from "firebase-admin/app";
import {setGlobalOptions} from "firebase-functions";

// Set global options for cost control
setGlobalOptions({
  maxInstances: 10,
  region: 'us-central1',
});

// Initialize Firebase Admin
initializeApp();

// ===== MESSAGING FUNCTIONS =====
export {
  onMessageCreateV2,
  markMessagesAsReadV2,
  createChatV2,
} from './messaging-v2';

// ===== CALLING FUNCTIONS =====
export {
  initiateCallV2,
  answerCallV2,
  rejectCallV2,
  endCallV2,
  updateCallSignalingV2,
} from './calls-v2';

// ===== NOTIFICATION FUNCTIONS =====
export {
  onFollowCreateV2,
  onFollowDeleteV2,
  onLikeCreateV2,
  onLikeDeleteV2,
  onCommentCreateV2,
  onShareCreateV2,
} from './notifications-v2';

console.log('🚀 Billionaires Social Backend v2 (Expanded) initialized!');
console.log('📱 Available functions:');
console.log('  💬 Messaging: onMessageCreateV2, markMessagesAsReadV2, createChatV2');
console.log('  📞 Calling: initiateCallV2, answerCallV2, rejectCallV2, endCallV2, updateCallSignalingV2');
console.log('  🔔 Notifications: onFollowCreateV2, onLikeCreateV2, onCommentCreateV2, onShareCreateV2');
