import {onDocumentCreated} from 'firebase-functions/v2/firestore';
import {on<PERSON>all, HttpsError} from 'firebase-functions/v2/https';
import {getFirestore, FieldValue} from 'firebase-admin/firestore';
import {getMessaging} from 'firebase-admin/messaging';
import {logger} from 'firebase-functions';

const db = getFirestore();

/**
 * Simple message notification function using Firebase Functions v2
 */
export const onMessageCreateV2 = onDocumentCreated('chats/{chatId}/messages/{messageId}', async (event) => {
  const snap = event.data;
  if (!snap) return;
  
  const message = snap.data();
  const chatId = event.params.chatId;
  const messageId = event.params.messageId;

  logger.info(`📨 Processing message ${messageId} in chat ${chatId}`);

  try {
    // Get chat participants
    const chatDoc = await db.collection('chats').doc(chatId).get();
    
    if (!chatDoc.exists) {
      logger.error(`Chat ${chatId} not found`);
      return;
    }

    const chatData = chatDoc.data()!;
    const participants = chatData.participants as string[];
    const senderId = message.senderId;

    // Send notification to all participants except sender
    const recipients = participants.filter((id: string) => id !== senderId);
    
    for (const recipientId of recipients) {
      await sendMessageNotification(recipientId, message, senderId, chatId);
    }

    // Update chat metadata
    await db.collection('chats').doc(chatId).update({
      lastMessage: message.content || message.type,
      lastMessageAt: message.timestamp,
      lastMessageBy: senderId,
      messageCount: FieldValue.increment(1),
      updatedAt: FieldValue.serverTimestamp(),
    });

    logger.info(`✅ Message ${messageId} processed successfully`);
  } catch (error) {
    logger.error('Error processing message:', error);
  }
});

/**
 * Simple function to mark messages as read using Firebase Functions v2
 */
export const markMessagesAsReadV2 = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const data = request.data;
  const { chatId, messageIds } = data;
  const userId = request.auth.uid;

  logger.info(`📖 Marking ${messageIds.length} messages as read for user ${userId}`);

  try {
    // Update read status for messages
    const batch = db.batch();
    for (const messageId of messageIds) {
      const messageRef = db.collection('chats').doc(chatId).collection('messages').doc(messageId);
      batch.update(messageRef, {
        [`readBy.${userId}`]: FieldValue.serverTimestamp(),
      });
    }

    // Reset unread count for user
    const userChatRef = db.collection('users').doc(userId).collection('chats').doc(chatId);
    batch.update(userChatRef, {
      unreadCount: 0,
      lastReadAt: FieldValue.serverTimestamp(),
    });

    await batch.commit();
    logger.info(`✅ Messages marked as read for user ${userId}`);

    return { success: true };
  } catch (error) {
    logger.error('Error marking messages as read:', error);
    throw new HttpsError('internal', 'Failed to mark messages as read');
  }
});

/**
 * Helper function to send message notification
 */
async function sendMessageNotification(
  recipientId: string,
  message: any,
  senderId: string,
  chatId: string
) {
  try {
    // Get recipient's FCM token
    const userDoc = await db.collection('users').doc(recipientId).get();
    
    if (!userDoc.exists) {
      logger.warn(`User ${recipientId} not found`);
      return;
    }

    const userData = userDoc.data()!;
    const fcmToken = userData.fcmToken;

    if (!fcmToken) {
      logger.warn(`No FCM token for user ${recipientId}`);
      return;
    }

    // Get sender information
    const senderDoc = await db.collection('users').doc(senderId).get();
    const senderName = senderDoc.exists ? 
      (senderDoc.data()!.username || 'Someone') : 'Someone';

    // Create FCM message
    const fcmMessage = {
      token: fcmToken,
      notification: {
        title: `Message from ${senderName}`,
        body: message.content || 'Sent a message',
      },
      data: {
        type: 'message',
        chatId: chatId,
        senderId: senderId,
        messageId: message.id || 'unknown',
      },
    };

    // Send notification
    await getMessaging().send(fcmMessage);
    
    // Update unread count
    await db.collection('users').doc(recipientId).collection('chats').doc(chatId).update({
      unreadCount: FieldValue.increment(1),
      lastActivity: FieldValue.serverTimestamp(),
    });

    logger.info(`📱 Message notification sent to ${recipientId}`);
  } catch (error) {
    logger.error(`❌ Error sending message notification to ${recipientId}:`, error);
  }
}

/**
 * Function to create a new chat using Firebase Functions v2
 */
export const createChatV2 = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const data = request.data;
  const { participants, type = 'direct', name } = data;
  const creatorId = request.auth.uid;

  logger.info(`Creating new chat with participants: ${participants.join(', ')}`);

  try {
    // Create chat document
    const chatRef = db.collection('chats').doc();
    const chatData = {
      participants: participants,
      type: type,
      name: name || null,
      createdBy: creatorId,
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
      messageCount: 0,
      lastMessage: null,
      lastMessageAt: null,
      lastMessageBy: null,
    };

    await chatRef.set(chatData);

    // Create chat entries in each participant's chat list
    const batch = db.batch();
    for (const participantId of participants) {
      const userChatRef = db.collection('users').doc(participantId).collection('chats').doc(chatRef.id);
      batch.set(userChatRef, {
        chatId: chatRef.id,
        chatType: type,
        participants: participants,
        createdAt: FieldValue.serverTimestamp(),
        lastActivity: FieldValue.serverTimestamp(),
        unreadCount: 0,
        isArchived: false,
        isMuted: false,
      });
    }

    await batch.commit();

    logger.info(`✅ Chat ${chatRef.id} created successfully`);
    return { 
      success: true, 
      chatId: chatRef.id,
      chatData: chatData 
    };
  } catch (error) {
    logger.error('Error creating chat:', error);
    throw new HttpsError('internal', 'Failed to create chat');
  }
});
