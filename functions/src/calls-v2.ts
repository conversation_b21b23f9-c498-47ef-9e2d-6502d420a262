import {onCall, HttpsError} from 'firebase-functions/v2/https';
import {getFirestore, FieldValue} from 'firebase-admin/firestore';
import {getMessaging} from 'firebase-admin/messaging';
import {logger} from 'firebase-functions';

const db = getFirestore();

/**
 * Initiate a video or voice call
 */
export const initiateCallV2 = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const data = request.data;
  const { recipientId, callType = 'voice', metadata = {} } = data;
  const callerId = request.auth.uid;

  logger.info(`📞 Initiating ${callType} call from ${callerId} to ${recipientId}`);

  try {
    // Check if recipient exists
    const recipientDoc = await db.collection('users').doc(recipientId).get();
    if (!recipientDoc.exists) {
      throw new HttpsError('not-found', 'Recipient not found');
    }

    // Create call document
    const callRef = db.collection('calls').doc();
    const callData = {
      callId: callRef.id,
      callerId: callerId,
      recipientId: recipientId,
      callType: callType, // 'voice' or 'video'
      status: 'ringing',
      participants: [callerId, recipientId],
      createdAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
      startedAt: null,
      endedAt: null,
      duration: 0,
      metadata: metadata,
      signalingData: {},
    };

    await callRef.set(callData);

    // Send call notification to recipient
    await sendCallNotification(recipientId, callerId, callRef.id, callType, 'incoming');

    // Log call event
    await db.collection('call_events').add({
      callId: callRef.id,
      eventType: 'initiated',
      userId: callerId,
      timestamp: FieldValue.serverTimestamp(),
      metadata: { callType, recipientId },
    });

    logger.info(`✅ Call ${callRef.id} initiated successfully`);
    return { 
      success: true, 
      callId: callRef.id,
      callData: callData 
    };
  } catch (error) {
    logger.error('Error initiating call:', error);
    throw new HttpsError('internal', 'Failed to initiate call');
  }
});

/**
 * Answer an incoming call
 */
export const answerCallV2 = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const data = request.data;
  const { callId, signalingData = {} } = data;
  const userId = request.auth.uid;

  logger.info(`📞 User ${userId} answering call ${callId}`);

  try {
    const callRef = db.collection('calls').doc(callId);
    const callDoc = await callRef.get();

    if (!callDoc.exists) {
      throw new HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is the recipient
    if (callData.recipientId !== userId) {
      throw new HttpsError('permission-denied', 'Not authorized to answer this call');
    }

    // Update call status
    await callRef.update({
      status: 'active',
      startedAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
      [`signalingData.${userId}`]: signalingData,
    });

    // Notify caller that call was answered
    await sendCallNotification(callData.callerId, userId, callId, callData.callType, 'answered');

    // Log call event
    await db.collection('call_events').add({
      callId: callId,
      eventType: 'answered',
      userId: userId,
      timestamp: FieldValue.serverTimestamp(),
    });

    logger.info(`✅ Call ${callId} answered successfully`);
    return { success: true };
  } catch (error) {
    logger.error('Error answering call:', error);
    throw new HttpsError('internal', 'Failed to answer call');
  }
});

/**
 * Reject an incoming call
 */
export const rejectCallV2 = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const data = request.data;
  const { callId, reason = 'declined' } = data;
  const userId = request.auth.uid;

  logger.info(`📞 User ${userId} rejecting call ${callId}`);

  try {
    const callRef = db.collection('calls').doc(callId);
    const callDoc = await callRef.get();

    if (!callDoc.exists) {
      throw new HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is the recipient
    if (callData.recipientId !== userId) {
      throw new HttpsError('permission-denied', 'Not authorized to reject this call');
    }

    // Update call status
    await callRef.update({
      status: 'rejected',
      endedAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
      endReason: reason,
    });

    // Notify caller that call was rejected
    await sendCallNotification(callData.callerId, userId, callId, callData.callType, 'rejected');

    // Log call event
    await db.collection('call_events').add({
      callId: callId,
      eventType: 'rejected',
      userId: userId,
      timestamp: FieldValue.serverTimestamp(),
      metadata: { reason },
    });

    logger.info(`✅ Call ${callId} rejected successfully`);
    return { success: true };
  } catch (error) {
    logger.error('Error rejecting call:', error);
    throw new HttpsError('internal', 'Failed to reject call');
  }
});

/**
 * End an active call
 */
export const endCallV2 = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const data = request.data;
  const { callId } = data;
  const userId = request.auth.uid;

  logger.info(`📞 User ${userId} ending call ${callId}`);

  try {
    const callRef = db.collection('calls').doc(callId);
    const callDoc = await callRef.get();

    if (!callDoc.exists) {
      throw new HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is a participant
    if (!callData.participants.includes(userId)) {
      throw new HttpsError('permission-denied', 'Not authorized to end this call');
    }

    // Calculate duration if call was active
    let duration = 0;
    if (callData.startedAt && callData.status === 'active') {
      const now = new Date();
      const startTime = callData.startedAt.toDate();
      duration = Math.floor((now.getTime() - startTime.getTime()) / 1000);
    }

    // Update call status
    await callRef.update({
      status: 'ended',
      endedAt: FieldValue.serverTimestamp(),
      updatedAt: FieldValue.serverTimestamp(),
      duration: duration,
      endedBy: userId,
    });

    // Notify other participant that call ended
    const otherParticipant = callData.participants.find((id: string) => id !== userId);
    if (otherParticipant) {
      await sendCallNotification(otherParticipant, userId, callId, callData.callType, 'ended');
    }

    // Log call event
    await db.collection('call_events').add({
      callId: callId,
      eventType: 'ended',
      userId: userId,
      timestamp: FieldValue.serverTimestamp(),
      metadata: { duration },
    });

    logger.info(`✅ Call ${callId} ended successfully (duration: ${duration}s)`);
    return { success: true, duration };
  } catch (error) {
    logger.error('Error ending call:', error);
    throw new HttpsError('internal', 'Failed to end call');
  }
});

/**
 * Update call signaling data (for WebRTC)
 */
export const updateCallSignalingV2 = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const data = request.data;
  const { callId, signalingData } = data;
  const userId = request.auth.uid;

  try {
    const callRef = db.collection('calls').doc(callId);
    const callDoc = await callRef.get();

    if (!callDoc.exists) {
      throw new HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is a participant
    if (!callData.participants.includes(userId)) {
      throw new HttpsError('permission-denied', 'Not authorized to update this call');
    }

    // Update signaling data
    await callRef.update({
      [`signalingData.${userId}`]: signalingData,
      updatedAt: FieldValue.serverTimestamp(),
    });

    return { success: true };
  } catch (error) {
    logger.error('Error updating call signaling:', error);
    throw new HttpsError('internal', 'Failed to update call signaling');
  }
});

/**
 * Helper function to send call notifications
 */
async function sendCallNotification(
  recipientId: string,
  senderId: string,
  callId: string,
  callType: string,
  notificationType: string
) {
  try {
    // Get recipient's FCM token
    const userDoc = await db.collection('users').doc(recipientId).get();
    
    if (!userDoc.exists) {
      logger.warn(`User ${recipientId} not found for call notification`);
      return;
    }

    const userData = userDoc.data()!;
    const fcmToken = userData.fcmToken;

    if (!fcmToken) {
      logger.warn(`No FCM token for user ${recipientId}`);
      return;
    }

    // Get sender information
    const senderDoc = await db.collection('users').doc(senderId).get();
    const senderName = senderDoc.exists ? 
      (senderDoc.data()!.username || 'Someone') : 'Someone';

    // Create notification message based on type
    let title = '';
    let body = '';
    
    switch (notificationType) {
      case 'incoming':
        title = `Incoming ${callType} call`;
        body = `${senderName} is calling you`;
        break;
      case 'answered':
        title = 'Call answered';
        body = `${senderName} answered your call`;
        break;
      case 'rejected':
        title = 'Call declined';
        body = `${senderName} declined your call`;
        break;
      case 'ended':
        title = 'Call ended';
        body = `Call with ${senderName} ended`;
        break;
    }

    // Create FCM message
    const fcmMessage = {
      token: fcmToken,
      notification: {
        title: title,
        body: body,
      },
      data: {
        type: 'call',
        callId: callId,
        callType: callType,
        senderId: senderId,
        notificationType: notificationType,
      },
    };

    // Send notification
    await getMessaging().send(fcmMessage);
    logger.info(`📱 Call notification sent to ${recipientId}: ${notificationType}`);
  } catch (error) {
    logger.error(`❌ Error sending call notification to ${recipientId}:`, error);
  }
}
