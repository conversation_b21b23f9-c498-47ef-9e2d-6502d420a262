import {initializeApp} from "firebase-admin/app";
import {setGlobalOptions} from "firebase-functions";

// ULTRA-OPTIMIZED global options for maximum resource efficiency
setGlobalOptions({
  maxInstances: 1,           // Reduced from 3 to 1 (66% reduction)
  region: 'us-central1',
  memory: '128MiB',          // Reduced from 256MiB to 128MiB (50% reduction)
  timeoutSeconds: 20,        // Reduced from 30 to 20 seconds (33% reduction)
  minInstances: 0,           // Ensure cold starts to save resources
});

// Initialize Firebase Admin
initializeApp();

// ===== CORE MESSAGING FUNCTIONS =====
export {
  onMessageCreateV2,
  markMessagesAsReadV2,
  createChatV2,
} from './messaging-v2';

// ===== CORE CALLING FUNCTIONS =====
export {
  initiateCallV2,
  answerCallV2,
  rejectCallV2,
  endCallV2,
  updateCallSignalingV2,
} from './calls-v2';

// ===== PRIORITY NOTIFICATION FUNCTIONS =====
export {
  onFollowCreateV2,
  onFollowDeleteV2,
  onLikeDeleteV2,
  onCommentCreateV2,
  onShareCreateV2,
} from './notifications-v2';

// ===== ESSENTIAL MODERATION FUNCTIONS =====
export {
  moderatePostV2,
  handleReportV2,
} from './moderation-v2';

console.log('🚀 Billionaires Social Backend v2 (ULTRA-OPTIMIZED) initialized!');
console.log('📱 Available functions:');
console.log('  💬 Messaging: onMessageCreateV2, markMessagesAsReadV2, createChatV2');
console.log('  📞 Calling: initiateCallV2, answerCallV2, rejectCallV2, endCallV2, updateCallSignalingV2');
console.log('  🔔 Notifications: onFollowCreateV2, onFollowDeleteV2, onLikeDeleteV2, onCommentCreateV2, onShareCreateV2');
console.log('  🛡️ Moderation: moderatePostV2, handleReportV2');
console.log('  ⚡ ULTRA-Optimized: 128MiB memory, 20s timeout, 1 max instance');
console.log('  📊 Total Functions: 15 (Essential functions only)');
console.log('  💾 Resource Usage: ~1.9 vCPUs (15 × 128MiB × 1 instance)');
