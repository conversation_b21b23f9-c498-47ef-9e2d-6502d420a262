import {onSchedule} from 'firebase-functions/v2/scheduler';
import {onCall, HttpsError} from 'firebase-functions/v2/https';
import {getFirestore, FieldValue, Timestamp} from 'firebase-admin/firestore';
import {logger} from 'firebase-functions';

const db = getFirestore();

/**
 * Daily cleanup job - runs every day at 2 AM UTC
 */
export const dailyCleanupV2 = onSchedule('0 2 * * *', async (event) => {
  logger.info('🧹 Starting daily cleanup job');

  try {
    await Promise.all([
      cleanupExpiredStories(),
      cleanupOldNotifications(),
      cleanupFailedUploads(),
      updateUserAnalytics(),
      cleanupExpiredCalls(),
    ]);

    logger.info('✅ Daily cleanup completed successfully');
  } catch (error) {
    logger.error('❌ Error in daily cleanup:', error);
  }
});

/**
 * Hourly cache warming - runs every hour
 */
export const hourlyCacheWarmV2 = onSchedule('0 * * * *', async (event) => {
  logger.info('🔥 Starting hourly cache warming');

  try {
    await Promise.all([
      warmTrendingContent(),
      warmPopularUsers(),
      warmRecentPosts(),
    ]);

    logger.info('✅ Cache warming completed successfully');
  } catch (error) {
    logger.error('❌ Error in cache warming:', error);
  }
});

/**
 * Weekly analytics aggregation - runs every Sunday at 3 AM UTC
 */
export const weeklyAnalyticsV2 = onSchedule('0 3 * * 0', async (event) => {
  logger.info('📊 Starting weekly analytics aggregation');

  try {
    await Promise.all([
      aggregateWeeklyUserStats(),
      aggregateWeeklyContentStats(),
      generateWeeklyInsights(),
    ]);

    logger.info('✅ Weekly analytics completed successfully');
  } catch (error) {
    logger.error('❌ Error in weekly analytics:', error);
  }
});

/**
 * Manual cleanup function for testing
 */
export const runCleanupV2 = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Verify admin permissions
  const userDoc = await db.collection('users').doc(request.auth.uid).get();
  if (!userDoc.exists || !userDoc.data()?.isAdmin) {
    throw new HttpsError('permission-denied', 'Admin access required');
  }

  const { operation } = request.data;

  try {
    switch (operation) {
      case 'stories':
        await cleanupExpiredStories();
        break;
      case 'notifications':
        await cleanupOldNotifications();
        break;
      case 'calls':
        await cleanupExpiredCalls();
        break;
      case 'cache':
        await warmTrendingContent();
        await warmPopularUsers();
        await warmRecentPosts();
        break;
      default:
        throw new HttpsError('invalid-argument', 'Invalid operation');
    }

    return { success: true, operation };
  } catch (error) {
    logger.error(`Error in manual cleanup (${operation}):`, error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    throw new HttpsError('internal', `Cleanup failed: ${errorMessage}`);
  }
});

/**
 * Clean up expired stories (older than 24 hours)
 */
async function cleanupExpiredStories() {
  logger.info('🗑️ Cleaning up expired stories');

  const oneDayAgo = Timestamp.fromDate(
    new Date(Date.now() - 24 * 60 * 60 * 1000)
  );

  const expiredStories = await db.collection('stories')
    .where('createdAt', '<', oneDayAgo)
    .where('isExpired', '==', false)
    .limit(100)
    .get();

  const batch = db.batch();
  let count = 0;

  for (const doc of expiredStories.docs) {
    batch.update(doc.ref, {
      isExpired: true,
      expiredAt: FieldValue.serverTimestamp(),
    });
    count++;
  }

  if (count > 0) {
    await batch.commit();
    logger.info(`✅ Marked ${count} stories as expired`);
  }
}

/**
 * Clean up old notifications (older than 30 days)
 */
async function cleanupOldNotifications() {
  logger.info('🗑️ Cleaning up old notifications');

  const thirtyDaysAgo = Timestamp.fromDate(
    new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
  );

  // Get all users to clean their notifications
  const users = await db.collection('users').select().limit(50).get();

  for (const userDoc of users.docs) {
    const oldNotifications = await db.collection('users')
      .doc(userDoc.id)
      .collection('notifications')
      .where('createdAt', '<', thirtyDaysAgo)
      .limit(50)
      .get();

    if (!oldNotifications.empty) {
      const batch = db.batch();
      oldNotifications.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      await batch.commit();
      logger.info(`✅ Deleted ${oldNotifications.size} old notifications for user ${userDoc.id}`);
    }
  }
}

/**
 * Clean up failed uploads (older than 1 hour)
 */
async function cleanupFailedUploads() {
  logger.info('🗑️ Cleaning up failed uploads');

  const oneHourAgo = Timestamp.fromDate(
    new Date(Date.now() - 60 * 60 * 1000)
  );

  const failedUploads = await db.collection('upload_sessions')
    .where('status', '==', 'failed')
    .where('createdAt', '<', oneHourAgo)
    .limit(100)
    .get();

  const batch = db.batch();
  let count = 0;

  for (const doc of failedUploads.docs) {
    batch.delete(doc.ref);
    count++;
  }

  if (count > 0) {
    await batch.commit();
    logger.info(`✅ Deleted ${count} failed upload sessions`);
  }
}

/**
 * Update user analytics daily
 */
async function updateUserAnalytics() {
  logger.info('📊 Updating user analytics');

  const yesterday = Timestamp.fromDate(
    new Date(Date.now() - 24 * 60 * 60 * 1000)
  );

  // Get active users from yesterday
  const activeUsers = await db.collection('user_activity')
    .where('lastActive', '>=', yesterday)
    .get();

  const batch = db.batch();
  let count = 0;

  for (const doc of activeUsers.docs) {
    const userId = doc.data().userId;
    
    // Update user's daily analytics
    const analyticsRef = db.collection('users').doc(userId).collection('analytics').doc('daily');
    batch.set(analyticsRef, {
      lastUpdated: FieldValue.serverTimestamp(),
      activeDate: yesterday,
    }, { merge: true });
    
    count++;
  }

  if (count > 0) {
    await batch.commit();
    logger.info(`✅ Updated analytics for ${count} active users`);
  }
}

/**
 * Clean up expired calls (older than 1 day)
 */
async function cleanupExpiredCalls() {
  logger.info('🗑️ Cleaning up expired calls');

  const oneDayAgo = Timestamp.fromDate(
    new Date(Date.now() - 24 * 60 * 60 * 1000)
  );

  const expiredCalls = await db.collection('calls')
    .where('createdAt', '<', oneDayAgo)
    .where('status', 'in', ['ringing', 'missed'])
    .limit(100)
    .get();

  const batch = db.batch();
  let count = 0;

  for (const doc of expiredCalls.docs) {
    batch.update(doc.ref, {
      status: 'expired',
      expiredAt: FieldValue.serverTimestamp(),
    });
    count++;
  }

  if (count > 0) {
    await batch.commit();
    logger.info(`✅ Marked ${count} calls as expired`);
  }
}

/**
 * Warm trending content cache
 */
async function warmTrendingContent() {
  logger.info('🔥 Warming trending content cache');

  const oneDayAgo = Timestamp.fromDate(
    new Date(Date.now() - 24 * 60 * 60 * 1000)
  );

  // Get trending posts
  const trendingPosts = await db.collection('posts')
    .where('createdAt', '>=', oneDayAgo)
    .orderBy('engagementScore', 'desc')
    .limit(50)
    .get();

  // Cache trending posts
  const cacheData = {
    posts: trendingPosts.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })),
    lastUpdated: FieldValue.serverTimestamp(),
  };

  await db.collection('cache').doc('trending_posts').set(cacheData);
  logger.info(`✅ Cached ${trendingPosts.size} trending posts`);
}

/**
 * Warm popular users cache
 */
async function warmPopularUsers() {
  logger.info('🔥 Warming popular users cache');

  // Get users with most followers
  const popularUsers = await db.collection('users')
    .orderBy('followerCount', 'desc')
    .limit(100)
    .get();

  // Cache popular users
  const cacheData = {
    users: popularUsers.docs.map(doc => ({
      id: doc.id,
      username: doc.data().username,
      displayName: doc.data().displayName,
      profilePicture: doc.data().profilePicture,
      followerCount: doc.data().followerCount,
      isVerified: doc.data().isVerified,
    })),
    lastUpdated: FieldValue.serverTimestamp(),
  };

  await db.collection('cache').doc('popular_users').set(cacheData);
  logger.info(`✅ Cached ${popularUsers.size} popular users`);
}

/**
 * Warm recent posts cache
 */
async function warmRecentPosts() {
  logger.info('🔥 Warming recent posts cache');

  const oneHourAgo = Timestamp.fromDate(
    new Date(Date.now() - 60 * 60 * 1000)
  );

  // Get recent posts
  const recentPosts = await db.collection('posts')
    .where('createdAt', '>=', oneHourAgo)
    .orderBy('createdAt', 'desc')
    .limit(100)
    .get();

  // Cache recent posts
  const cacheData = {
    posts: recentPosts.docs.map(doc => ({
      id: doc.id,
      ...doc.data(),
    })),
    lastUpdated: FieldValue.serverTimestamp(),
  };

  await db.collection('cache').doc('recent_posts').set(cacheData);
  logger.info(`✅ Cached ${recentPosts.size} recent posts`);
}

/**
 * Aggregate weekly user statistics
 */
async function aggregateWeeklyUserStats() {
  logger.info('📊 Aggregating weekly user stats');

  const oneWeekAgo = Timestamp.fromDate(
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  );

  // Get user activity from the past week
  const userActivity = await db.collection('user_activity')
    .where('lastActive', '>=', oneWeekAgo)
    .get();

  const stats = {
    activeUsers: userActivity.size,
    weekStart: oneWeekAgo,
    weekEnd: Timestamp.now(),
    generatedAt: FieldValue.serverTimestamp(),
  };

  await db.collection('analytics').doc('weekly_user_stats').set(stats);
  logger.info(`✅ Aggregated stats for ${stats.activeUsers} active users`);
}

/**
 * Aggregate weekly content statistics
 */
async function aggregateWeeklyContentStats() {
  logger.info('📊 Aggregating weekly content stats');

  const oneWeekAgo = Timestamp.fromDate(
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  );

  // Get posts from the past week
  const weeklyPosts = await db.collection('posts')
    .where('createdAt', '>=', oneWeekAgo)
    .get();

  // Get stories from the past week
  const weeklyStories = await db.collection('stories')
    .where('createdAt', '>=', oneWeekAgo)
    .get();

  const stats = {
    postsCreated: weeklyPosts.size,
    storiesCreated: weeklyStories.size,
    weekStart: oneWeekAgo,
    weekEnd: Timestamp.now(),
    generatedAt: FieldValue.serverTimestamp(),
  };

  await db.collection('analytics').doc('weekly_content_stats').set(stats);
  logger.info(`✅ Aggregated content stats: ${stats.postsCreated} posts, ${stats.storiesCreated} stories`);
}

/**
 * Generate weekly insights
 */
async function generateWeeklyInsights() {
  logger.info('💡 Generating weekly insights');

  const oneWeekAgo = Timestamp.fromDate(
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  );

  // Get top performing posts
  const topPosts = await db.collection('posts')
    .where('createdAt', '>=', oneWeekAgo)
    .orderBy('engagementScore', 'desc')
    .limit(10)
    .get();

  // Get most active users
  const activeUsers = await db.collection('user_activity')
    .where('lastActive', '>=', oneWeekAgo)
    .orderBy('activityScore', 'desc')
    .limit(10)
    .get();

  const insights = {
    topPosts: topPosts.docs.map(doc => ({
      id: doc.id,
      engagementScore: doc.data().engagementScore,
      userId: doc.data().userId,
    })),
    mostActiveUsers: activeUsers.docs.map(doc => ({
      userId: doc.data().userId,
      activityScore: doc.data().activityScore,
    })),
    weekStart: oneWeekAgo,
    weekEnd: Timestamp.now(),
    generatedAt: FieldValue.serverTimestamp(),
  };

  await db.collection('analytics').doc('weekly_insights').set(insights);
  logger.info(`✅ Generated weekly insights`);
}
