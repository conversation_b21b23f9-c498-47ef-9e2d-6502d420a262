import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

/**
 * Simple message notification function
 * Triggered when a new message is created in a chat
 */
export const onMessageCreateSimple = functions.firestore
  .document('chats/{chatId}/messages/{messageId}')
  .onCreate(async (snap, context) => {
    try {
      const message = snap.data();
      const chatId = context.params.chatId;
      const messageId = context.params.messageId;

      console.log(`📨 Processing message ${messageId} in chat ${chatId}`);

      // Get chat participants
      const chatDoc = await admin.firestore()
        .collection('chats')
        .doc(chatId)
        .get();

      if (!chatDoc.exists) {
        console.error(`Chat ${chatId} not found`);
        return;
      }

      const chatData = chatDoc.data()!;
      const participants = chatData.participants as string[];
      const senderId = message.senderId;

      // Send notification to all participants except sender
      const recipients = participants.filter(id => id !== senderId);
      
      for (const recipientId of recipients) {
        await sendSimpleNotification(recipientId, message, senderId);
      }

      // Update chat metadata
      await admin.firestore().collection('chats').doc(chatId).update({
        lastMessage: message.content || message.type,
        lastMessageAt: message.timestamp,
        lastMessageBy: senderId,
        updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      });

      console.log(`✅ Message ${messageId} processed successfully`);
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });

/**
 * Simple notification sender
 */
async function sendSimpleNotification(
  recipientId: string,
  message: any,
  senderId: string
) {
  try {
    // Get recipient's FCM token
    const userDoc = await admin.firestore()
      .collection('users')
      .doc(recipientId)
      .get();

    if (!userDoc.exists) return;

    const userData = userDoc.data()!;
    const fcmToken = userData.fcmToken;

    if (!fcmToken) return;

    // Get sender information
    const senderDoc = await admin.firestore()
      .collection('users')
      .doc(senderId)
      .get();

    const senderName = senderDoc.exists ? 
      (senderDoc.data()!.username || 'Someone') : 'Someone';

    // Create FCM message
    const fcmMessage: admin.messaging.Message = {
      token: fcmToken,
      notification: {
        title: `Message from ${senderName}`,
        body: message.content || 'Sent a message',
      },
      data: {
        type: 'message',
        chatId: message.chatId || 'unknown',
        senderId,
        messageId: message.id || 'unknown',
      },
    };

    // Send notification
    await admin.messaging().send(fcmMessage);
    console.log(`📱 Notification sent to ${recipientId}`);
  } catch (error) {
    console.error(`❌ Error sending notification to ${recipientId}:`, error);
  }
}

/**
 * Simple function to mark messages as read
 */
export const markMessagesAsReadSimple = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    const { chatId, messageIds } = data;
    const userId = context.auth.uid;

    console.log(`📖 Marking ${messageIds.length} messages as read for user ${userId}`);

    // Update read status for messages
    const batch = admin.firestore().batch();
    for (const messageId of messageIds) {
      const messageRef = admin.firestore()
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .doc(messageId);
      
      batch.update(messageRef, {
        [`readBy.${userId}`]: admin.firestore.FieldValue.serverTimestamp(),
      });
    }

    await batch.commit();
    console.log(`✅ Messages marked as read for user ${userId}`);

    return { success: true };
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw new functions.https.HttpsError('internal', 'Failed to mark messages as read');
  }
});
