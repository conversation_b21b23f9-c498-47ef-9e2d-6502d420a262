import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

const db = admin.firestore();

/**
 * Initiate a voice or video call
 */
export const initiateCall = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    const { recipientId, callType, chatId } = data; // callType: 'voice' | 'video'
    const callerId = context.auth.uid;

    console.log(`📞 Initiating ${callType} call from ${callerId} to ${recipientId}`);

    // Validate input
    if (!recipientId || !callType || !['voice', 'video'].includes(callType)) {
      throw new functions.https.HttpsError('invalid-argument', 'Invalid call parameters');
    }

    // Check if recipient exists
    const recipientDoc = await db.collection('users').doc(recipientId).get();
    if (!recipientDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Recipient not found');
    }

    // Check if there's already an active call between these users
    const existingCall = await db.collection('calls')
      .where('participants', 'array-contains', callerId)
      .where('status', 'in', ['ringing', 'active'])
      .limit(1)
      .get();

    if (!existingCall.empty) {
      throw new functions.https.HttpsError('failed-precondition', 'User already in a call');
    }

    // Create call document
    const callDoc = await db.collection('calls').add({
      callerId,
      recipientId,
      callType,
      chatId: chatId || null,
      status: 'ringing',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      participants: [callerId, recipientId],
      signaling: {
        offers: {},
        answers: {},
        iceCandidates: {},
      },
    });

    // Send call notification to recipient
    await sendCallNotification(recipientId, callerId, callDoc.id, callType);

    // Set call timeout (30 seconds)
    setTimeout(async () => {
      const callSnapshot = await callDoc.get();
      if (callSnapshot.exists && callSnapshot.data()?.status === 'ringing') {
        await callDoc.update({
          status: 'missed',
          endedAt: admin.firestore.FieldValue.serverTimestamp(),
          endReason: 'timeout',
        });
        console.log(`⏰ Call ${callDoc.id} timed out`);
      }
    }, 30000);

    console.log(`✅ Call ${callDoc.id} initiated successfully`);
    return { callId: callDoc.id, success: true };
  } catch (error) {
    console.error('Error initiating call:', error);
    throw error;
  }
});

/**
 * Answer an incoming call
 */
export const answerCall = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    const { callId } = data;
    const userId = context.auth.uid;

    console.log(`📞 User ${userId} answering call ${callId}`);

    // Get call document
    const callDoc = await db.collection('calls').doc(callId).get();
    if (!callDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is the recipient
    if (callData.recipientId !== userId) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to answer this call');
    }

    // Check if call is still ringing
    if (callData.status !== 'ringing') {
      throw new functions.https.HttpsError('failed-precondition', 'Call is no longer available');
    }

    // Update call status to active
    await callDoc.ref.update({
      status: 'active',
      answeredAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Notify caller that call was answered
    await notifyCallAnswered(callData.callerId, callId);

    console.log(`✅ Call ${callId} answered successfully`);
    return { success: true };
  } catch (error) {
    console.error('Error answering call:', error);
    throw error;
  }
});

/**
 * Reject an incoming call
 */
export const rejectCall = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    const { callId } = data;
    const userId = context.auth.uid;

    console.log(`📞 User ${userId} rejecting call ${callId}`);

    // Get call document
    const callDoc = await db.collection('calls').doc(callId).get();
    if (!callDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is the recipient
    if (callData.recipientId !== userId) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to reject this call');
    }

    // Update call status
    await callDoc.ref.update({
      status: 'rejected',
      endedAt: admin.firestore.FieldValue.serverTimestamp(),
      endReason: 'rejected',
    });

    // Notify caller that call was rejected
    await notifyCallRejected(callData.callerId, callId);

    console.log(`✅ Call ${callId} rejected successfully`);
    return { success: true };
  } catch (error) {
    console.error('Error rejecting call:', error);
    throw error;
  }
});

/**
 * End an active call
 */
export const endCall = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    const { callId } = data;
    const userId = context.auth.uid;

    console.log(`📞 User ${userId} ending call ${callId}`);

    // Get call document
    const callDoc = await db.collection('calls').doc(callId).get();
    if (!callDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is a participant
    if (!callData.participants.includes(userId)) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to end this call');
    }

    // Calculate call duration
    const endedAt = admin.firestore.Timestamp.now();
    let duration = 0;
    if (callData.answeredAt) {
      duration = endedAt.seconds - callData.answeredAt.seconds;
    }

    // Update call status
    await callDoc.ref.update({
      status: 'ended',
      endedAt,
      endedBy: userId,
      duration,
      endReason: 'ended_by_user',
    });

    // Notify other participant that call ended
    const otherParticipant = callData.participants.find((id: string) => id !== userId);
    if (otherParticipant) {
      await notifyCallEnded(otherParticipant, callId, duration);
    }

    // Log call analytics
    await logCallAnalytics(callData, duration);

    console.log(`✅ Call ${callId} ended successfully (duration: ${duration}s)`);
    return { success: true, duration };
  } catch (error) {
    console.error('Error ending call:', error);
    throw error;
  }
});

/**
 * Handle WebRTC signaling (offers, answers, ICE candidates)
 */
export const updateCallSignaling = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    const { callId, type, payload } = data; // type: 'offer' | 'answer' | 'ice-candidate'
    const userId = context.auth.uid;

    // Get call document
    const callDoc = await db.collection('calls').doc(callId).get();
    if (!callDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Call not found');
    }

    const callData = callDoc.data()!;

    // Verify user is a participant
    if (!callData.participants.includes(userId)) {
      throw new functions.https.HttpsError('permission-denied', 'Not authorized to update signaling');
    }

    // Update signaling data
    const updateData: any = {};
    if (type === 'offer') {
      updateData[`signaling.offers.${userId}`] = payload;
    } else if (type === 'answer') {
      updateData[`signaling.answers.${userId}`] = payload;
    } else if (type === 'ice-candidate') {
      updateData[`signaling.iceCandidates.${userId}`] = admin.firestore.FieldValue.arrayUnion(payload);
    }

    await callDoc.ref.update(updateData);

    return { success: true };
  } catch (error) {
    console.error('Error updating call signaling:', error);
    throw error;
  }
});

/**
 * Helper function to send call notification
 */
async function sendCallNotification(
  recipientId: string,
  callerId: string,
  callId: string,
  callType: string
) {
  try {
    // Get recipient's FCM token
    const userDoc = await db.collection('users').doc(recipientId).get();
    if (!userDoc.exists) return;

    const userData = userDoc.data()!;
    const fcmToken = userData.fcmToken;
    const notificationSettings = userData.notificationSettings || {};

    // Check if user has disabled call notifications
    if (!fcmToken || notificationSettings.calls === false) return;

    // Get caller information
    const callerDoc = await db.collection('users').doc(callerId).get();
    const callerName = callerDoc.exists ? 
      (callerDoc.data()!.username || callerDoc.data()!.displayName || 'Someone') : 'Someone';

    // Create FCM message
    const fcmMessage: admin.messaging.Message = {
      token: fcmToken,
      notification: {
        title: `Incoming ${callType} call`,
        body: `${callerName} is calling you`,
      },
      data: {
        type: 'call',
        callId,
        callerId,
        callType,
        callerName,
        action: 'incoming',
      },
      android: {
        notification: {
          channelId: 'calls',
          priority: 'max',
          sound: 'default',
          icon: 'ic_call',
          color: '#D4AF37',
          tag: callId,
        },
        data: {
          click_action: 'FLUTTER_NOTIFICATION_CLICK',
        },
      },
      apns: {
        payload: {
          aps: {
            badge: 1,
            sound: 'default',
            category: 'CALL',
            'content-available': 1,
          },
        },
      },
    };

    await admin.messaging().send(fcmMessage);
    console.log(`📱 Call notification sent to ${recipientId}`);
  } catch (error) {
    console.error(`❌ Error sending call notification to ${recipientId}:`, error);
  }
}

/**
 * Helper function to notify caller that call was answered
 */
async function notifyCallAnswered(callerId: string, callId: string) {
  try {
    const userDoc = await db.collection('users').doc(callerId).get();
    if (!userDoc.exists) return;

    const userData = userDoc.data()!;
    const fcmToken = userData.fcmToken;
    if (!fcmToken) return;

    const fcmMessage: admin.messaging.Message = {
      token: fcmToken,
      data: {
        type: 'call_answered',
        callId,
        action: 'answered',
      },
    };

    await admin.messaging().send(fcmMessage);
    console.log(`📱 Call answered notification sent to ${callerId}`);
  } catch (error) {
    console.error(`❌ Error sending call answered notification:`, error);
  }
}

/**
 * Helper function to notify caller that call was rejected
 */
async function notifyCallRejected(callerId: string, callId: string) {
  try {
    const userDoc = await db.collection('users').doc(callerId).get();
    if (!userDoc.exists) return;

    const userData = userDoc.data()!;
    const fcmToken = userData.fcmToken;
    if (!fcmToken) return;

    const fcmMessage: admin.messaging.Message = {
      token: fcmToken,
      data: {
        type: 'call_rejected',
        callId,
        action: 'rejected',
      },
    };

    await admin.messaging().send(fcmMessage);
    console.log(`📱 Call rejected notification sent to ${callerId}`);
  } catch (error) {
    console.error(`❌ Error sending call rejected notification:`, error);
  }
}

/**
 * Helper function to notify participant that call ended
 */
async function notifyCallEnded(participantId: string, callId: string, duration: number) {
  try {
    const userDoc = await db.collection('users').doc(participantId).get();
    if (!userDoc.exists) return;

    const userData = userDoc.data()!;
    const fcmToken = userData.fcmToken;
    if (!fcmToken) return;

    const fcmMessage: admin.messaging.Message = {
      token: fcmToken,
      data: {
        type: 'call_ended',
        callId,
        duration: duration.toString(),
        action: 'ended',
      },
    };

    await admin.messaging().send(fcmMessage);
    console.log(`📱 Call ended notification sent to ${participantId}`);
  } catch (error) {
    console.error(`❌ Error sending call ended notification:`, error);
  }
}

/**
 * Helper function to log call analytics
 */
async function logCallAnalytics(callData: any, duration: number) {
  try {
    await db.collection('call_analytics').add({
      callId: callData.id,
      callerId: callData.callerId,
      recipientId: callData.recipientId,
      callType: callData.callType,
      duration,
      status: callData.status,
      createdAt: callData.createdAt,
      answeredAt: callData.answeredAt,
      endedAt: admin.firestore.FieldValue.serverTimestamp(),
      endReason: callData.endReason,
    });

    console.log(`📊 Call analytics logged for call ${callData.id}`);
  } catch (error) {
    console.error('Error logging call analytics:', error);
  }
}
