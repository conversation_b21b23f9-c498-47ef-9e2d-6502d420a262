import {initializeApp} from "firebase-admin/app";
import {setGlobalOptions} from "firebase-functions";

// Set global options for cost control
setGlobalOptions({
  maxInstances: 10,
  region: 'us-central1',
});

// Initialize Firebase Admin
initializeApp();

// Test only calling functions
export {
  initiateCallV2,
  answerCallV2,
  rejectCallV2,
  endCallV2,
  updateCallSignalingV2,
} from './calls-v2';

console.log('🚀 Testing Calling Functions Only');
