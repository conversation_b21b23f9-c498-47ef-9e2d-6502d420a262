import {onDocumentCreated, onDocumentUpdated} from 'firebase-functions/v2/firestore';
import {onCall, HttpsError} from 'firebase-functions/v2/https';
import {getFirestore, FieldValue} from 'firebase-admin/firestore';
import {getMessaging} from 'firebase-admin/messaging';

const db = getFirestore();

/**
 * Triggered when a new message is created in a chat
 * Handles message delivery, notifications, and chat metadata updates
 */
export const onMessageCreate = onDocumentCreated('chats/{chatId}/messages/{messageId}', async (event) => {
  const snap = event.data;
  if (!snap) return;

  const context = event.params;
    try {
      const message = snap.data();
      const chatId = context.params.chatId;
      const messageId = context.params.messageId;

      console.log(`📨 Processing new message ${messageId} in chat ${chatId}`);

      // Get chat document
      const chatDoc = await db.collection('chats').doc(chatId).get();
      if (!chatDoc.exists) {
        console.error(`Chat ${chatId} not found`);
        return;
      }

      const chatData = chatDoc.data()!;
      const participants = chatData.participants as string[];
      const senderId = message.senderId;

      // Update chat metadata
      await updateChatMetadata(chatId, message, chatData);

      // Send notifications to all participants except sender
      const recipients = participants.filter(id => id !== senderId);
      await Promise.all(
        recipients.map(recipientId => 
          sendMessageNotification(recipientId, message, chatData, senderId)
        )
      );

      // Update unread counts for recipients
      await Promise.all(
        recipients.map(recipientId => 
          updateUnreadCount(recipientId, chatId)
        )
      );

      console.log(`✅ Message ${messageId} processed successfully`);
    } catch (error) {
      console.error('Error processing message:', error);
    }
  });

/**
 * Triggered when a new chat is created
 * Initializes chat metadata and participant chat lists
 */
export const onChatCreate = onDocumentCreated('chats/{chatId}', async (event) => {
  const snap = event.data;
  if (!snap) return;

  const context = event.params;
    try {
      const chatData = snap.data();
      const chatId = context.params.chatId;

      console.log(`💬 Initializing new chat ${chatId}`);

      // Initialize chat metadata
      await snap.ref.update({
        messageCount: 0,
        lastMessage: null,
        lastMessageAt: null,
        lastMessageBy: null,
        createdAt: FieldValue.serverTimestamp(),
        updatedAt: FieldValue.serverTimestamp(),
      });

      // Create chat entries in each participant's chat list
      const participants = chatData.participants as string[];
      await Promise.all(
        participants.map(participantId => 
          createUserChatEntry(participantId, chatId, chatData)
        )
      );

      console.log(`✅ Chat ${chatId} initialized successfully`);
    } catch (error) {
      console.error('Error initializing chat:', error);
    }
  });

/**
 * Triggered when a message is updated (e.g., edited, deleted)
 * Updates chat metadata and sends update notifications
 */
export const onMessageUpdate = onDocumentUpdated('chats/{chatId}/messages/{messageId}', async (event) => {
  const change = event.data;
  if (!change) return;

  const context = event.params;
    try {
      const before = change.before.data();
      const after = change.after.data();
      const chatId = context.params.chatId;

      // Handle message deletion
      if (after.isDeleted && !before.isDeleted) {
        await handleMessageDeletion(chatId, after);
      }

      // Handle message editing
      if (after.isEdited && !before.isEdited) {
        await handleMessageEdit(chatId, after);
      }

      console.log(`✅ Message update processed for chat ${chatId}`);
    } catch (error) {
      console.error('Error processing message update:', error);
    }
  });

/**
 * Cloud function to mark messages as read
 */
export const markMessagesAsRead = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError('unauthenticated', 'User must be authenticated');
  }

  const data = request.data;

  try {
    const { chatId, messageIds } = data;
    const userId = request.auth.uid;

    // Update read status for messages
    const batch = db.batch();
    for (const messageId of messageIds) {
      const messageRef = db.collection('chats').doc(chatId).collection('messages').doc(messageId);
      batch.update(messageRef, {
        [`readBy.${userId}`]: FieldValue.serverTimestamp(),
      });
    }

    // Reset unread count for user
    const userChatRef = db.collection('users').doc(userId).collection('chats').doc(chatId);
    batch.update(userChatRef, {
      unreadCount: 0,
      lastReadAt: FieldValue.serverTimestamp(),
    });

    await batch.commit();

    return { success: true };
  } catch (error) {
    console.error('Error marking messages as read:', error);
    throw new HttpsError('internal', 'Failed to mark messages as read');
  }
});

/**
 * Helper function to update chat metadata
 */
async function updateChatMetadata(chatId: string, message: any, chatData: any) {
  await db.collection('chats').doc(chatId).update({
    lastMessage: message.content || message.type,
    lastMessageAt: message.timestamp,
    lastMessageBy: message.senderId,
    messageCount: FieldValue.increment(1),
    updatedAt: FieldValue.serverTimestamp(),
  });
}

/**
 * Helper function to send message notification
 */
async function sendMessageNotification(
  recipientId: string,
  message: any,
  chatData: any,
  senderId: string
) {
  try {
    // Get recipient's FCM token and notification preferences
    const userDoc = await db.collection('users').doc(recipientId).get();
    if (!userDoc.exists) return;

    const userData = userDoc.data()!;
    const fcmToken = userData.fcmToken;
    const notificationSettings = userData.notificationSettings || {};

    // Check if user has disabled message notifications
    if (!fcmToken || notificationSettings.messages === false) return;

    // Get sender information
    const senderDoc = await db.collection('users').doc(senderId).get();
    const senderName = senderDoc.exists ? 
      (senderDoc.data()!.username || senderDoc.data()!.displayName || 'Someone') : 'Someone';

    // Prepare notification content
    let notificationBody = '';
    if (message.type === 'text') {
      notificationBody = message.content.length > 50 ? 
        `${message.content.substring(0, 50)}...` : message.content;
    } else if (message.type === 'image') {
      notificationBody = '📷 Sent a photo';
    } else if (message.type === 'video') {
      notificationBody = '🎥 Sent a video';
    } else if (message.type === 'audio') {
      notificationBody = '🎵 Sent an audio message';
    } else {
      notificationBody = 'Sent a message';
    }

    // Create FCM message
    const fcmMessage: admin.messaging.Message = {
      token: fcmToken,
      notification: {
        title: `Message from ${senderName}`,
        body: notificationBody,
      },
      data: {
        type: 'message',
        chatId: chatData.id || 'unknown',
        senderId,
        messageId: message.id || 'unknown',
        senderName,
      },
      android: {
        notification: {
          channelId: 'messages',
          priority: 'high',
          sound: 'default',
          icon: 'ic_message',
          color: '#D4AF37',
        },
        data: {
          click_action: 'FLUTTER_NOTIFICATION_CLICK',
        },
      },
      apns: {
        payload: {
          aps: {
            badge: 1,
            sound: 'default',
            category: 'MESSAGE',
            'mutable-content': 1,
          },
        },
        fcmOptions: {
          imageUrl: message.type === 'image' ? message.mediaUrl : undefined,
        },
      },
    };

    // Send notification
    await getMessaging().send(fcmMessage);

    // Log notification event
    await db.collection('notification_events').add({
      userId: recipientId,
      type: 'message',
      chatId: chatData.id,
      senderId,
      messageId: message.id,
      timestamp: FieldValue.serverTimestamp(),
      success: true,
    });

    console.log(`📱 Message notification sent to ${recipientId}`);
  } catch (error) {
    console.error(`❌ Error sending message notification to ${recipientId}:`, error);
    
    // Log failed notification
    await db.collection('notification_events').add({
      userId: recipientId,
      type: 'message',
      chatId: chatData.id,
      senderId,
      messageId: message.id,
      timestamp: FieldValue.serverTimestamp(),
      success: false,
      error: error.message,
    });
  }
}

/**
 * Helper function to update unread count
 */
async function updateUnreadCount(userId: string, chatId: string) {
  const userChatRef = db.collection('users').doc(userId).collection('chats').doc(chatId);
  await userChatRef.update({
    unreadCount: FieldValue.increment(1),
    lastActivity: FieldValue.serverTimestamp(),
  });
}

/**
 * Helper function to create user chat entry
 */
async function createUserChatEntry(userId: string, chatId: string, chatData: any) {
  await db.collection('users').doc(userId).collection('chats').doc(chatId).set({
    chatId,
    chatType: chatData.type || 'direct',
    participants: chatData.participants,
    createdAt: FieldValue.serverTimestamp(),
    lastActivity: FieldValue.serverTimestamp(),
    unreadCount: 0,
    isArchived: false,
    isMuted: false,
  });
}

/**
 * Helper function to handle message deletion
 */
async function handleMessageDeletion(chatId: string, message: any) {
  // Update chat's last message if this was the last message
  const chatDoc = await db.collection('chats').doc(chatId).get();
  if (chatDoc.exists) {
    const chatData = chatDoc.data()!;
    if (chatData.lastMessageBy === message.senderId && 
        chatData.lastMessageAt?.seconds === message.timestamp?.seconds) {
      
      // Find the previous message
      const previousMessages = await db.collection('chats')
        .doc(chatId)
        .collection('messages')
        .where('isDeleted', '==', false)
        .orderBy('timestamp', 'desc')
        .limit(1)
        .get();

      if (!previousMessages.empty) {
        const prevMessage = previousMessages.docs[0].data();
        await db.collection('chats').doc(chatId).update({
          lastMessage: prevMessage.content || prevMessage.type,
          lastMessageAt: prevMessage.timestamp,
          lastMessageBy: prevMessage.senderId,
        });
      } else {
        await db.collection('chats').doc(chatId).update({
          lastMessage: null,
          lastMessageAt: null,
          lastMessageBy: null,
        });
      }
    }
  }
}

/**
 * Helper function to handle message editing
 */
async function handleMessageEdit(chatId: string, message: any) {
  // Update chat's last message if this was the last message
  const chatDoc = await db.collection('chats').doc(chatId).get();
  if (chatDoc.exists) {
    const chatData = chatDoc.data()!;
    if (chatData.lastMessageBy === message.senderId && 
        chatData.lastMessageAt?.seconds === message.timestamp?.seconds) {
      
      await db.collection('chats').doc(chatId).update({
        lastMessage: message.content || message.type,
        updatedAt: FieldValue.serverTimestamp(),
      });
    }
  }
}
