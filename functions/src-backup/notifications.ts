import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

/**
 * Send notification when someone follows a user
 */
export const onFollowCreate = functions.firestore
  .document('users/{userId}/followers/{followerId}')
  .onCreate(async (snap, context) => {
    try {
      const followerId = context.params.followerId;
      const userId = context.params.userId;

      console.log(`👥 Processing follow notification: ${followerId} -> ${userId}`);

      // Don't send notification if user follows themselves
      if (followerId === userId) return;

      // Get follower information
      const followerDoc = await db.collection('users').doc(followerId).get();
      if (!followerDoc.exists) return;

      const followerData = followerDoc.data()!;
      const followerName = followerData.username || followerData.displayName || 'Someone';

      await sendEnhancedNotification(userId, {
        type: 'follow',
        title: 'New Follower',
        body: `${followerName} started following you`,
        data: {
          followerId,
          followerName,
          followerAvatar: followerData.profilePicture || '',
        },
      });

      console.log(`✅ Follow notification sent to ${userId}`);
    } catch (error) {
      console.error('Error processing follow notification:', error);
    }
  });

/**
 * Send notification when someone likes a post
 */
export const onLikeCreate = functions.firestore
  .document('posts/{postId}/likes/{likeId}')
  .onCreate(async (snap, context) => {
    try {
      const likeData = snap.data();
      const postId = context.params.postId;
      const likerId = likeData.userId;

      console.log(`❤️ Processing like notification for post ${postId}`);

      // Get post information
      const postDoc = await db.collection('posts').doc(postId).get();
      if (!postDoc.exists) return;

      const postData = postDoc.data()!;
      const authorId = postData.userId;

      // Don't notify if user likes their own post
      if (authorId === likerId) return;

      // Get liker information
      const likerDoc = await db.collection('users').doc(likerId).get();
      if (!likerDoc.exists) return;

      const likerData = likerDoc.data()!;
      const likerName = likerData.username || likerData.displayName || 'Someone';

      await sendEnhancedNotification(authorId, {
        type: 'like',
        title: 'Post Liked',
        body: `${likerName} liked your post`,
        data: {
          postId,
          likerId,
          likerName,
          likerAvatar: likerData.profilePicture || '',
          postContent: postData.content?.substring(0, 100) || '',
        },
      });

      console.log(`✅ Like notification sent to ${authorId}`);
    } catch (error) {
      console.error('Error processing like notification:', error);
    }
  });

/**
 * Send notification when someone comments on a post
 */
export const onCommentCreate = functions.firestore
  .document('posts/{postId}/comments/{commentId}')
  .onCreate(async (snap, context) => {
    try {
      const commentData = snap.data();
      const postId = context.params.postId;
      const commentId = context.params.commentId;
      const commenterId = commentData.userId;

      console.log(`💬 Processing comment notification for post ${postId}`);

      // Get post information
      const postDoc = await db.collection('posts').doc(postId).get();
      if (!postDoc.exists) return;

      const postData = postDoc.data()!;
      const authorId = postData.userId;

      // Don't notify if user comments on their own post
      if (authorId === commenterId) return;

      // Get commenter information
      const commenterDoc = await db.collection('users').doc(commenterId).get();
      if (!commenterDoc.exists) return;

      const commenterData = commenterDoc.data()!;
      const commenterName = commenterData.username || commenterData.displayName || 'Someone';

      const commentText = commentData.content?.substring(0, 50) || 'commented on your post';

      await sendEnhancedNotification(authorId, {
        type: 'comment',
        title: 'New Comment',
        body: `${commenterName}: ${commentText}`,
        data: {
          postId,
          commentId,
          commenterId,
          commenterName,
          commenterAvatar: commenterData.profilePicture || '',
          commentContent: commentData.content || '',
          postContent: postData.content?.substring(0, 100) || '',
        },
      });

      console.log(`✅ Comment notification sent to ${authorId}`);
    } catch (error) {
      console.error('Error processing comment notification:', error);
    }
  });

/**
 * Cloud Function to send real-time push notifications
 * Triggered when a new message is created
 */
export const sendMessageNotification = functions.firestore
  .document('chats/{chatId}/messages/{messageId}')
  .onCreate(async (snap, context) => {
    try {
      const message = snap.data();
      const chatId = context.params.chatId;
      const messageId = context.params.messageId;

      console.log(`📱 Processing notification for message ${messageId} in chat ${chatId}`);

      // Get chat participants
      const chatDoc = await admin.firestore()
        .collection('chats')
        .doc(chatId)
        .get();

      if (!chatDoc.exists) {
        console.error(`❌ Chat ${chatId} not found`);
        return;
      }

      const chatData = chatDoc.data()!;
      const participants = chatData.participants || [];

      // Get sender information
      const senderDoc = await admin.firestore()
        .collection('users')
        .doc(message.senderId)
        .get();

      const senderData = senderDoc.exists ? senderDoc.data()! : {};
      const senderName = senderData.username || senderData.displayName || 'Someone';
      const senderAvatar = senderData.profilePictureUrl || '';

      // Send notification to all participants except sender
      const notificationPromises = participants
        .filter((uid: string) => uid !== message.senderId)
        .map(async (uid: string) => {
          try {
            // Get user's FCM token and notification preferences
            const userDoc = await admin.firestore()
              .collection('users')
              .doc(uid)
              .get();

            if (!userDoc.exists) {
              console.warn(`⚠️ User ${uid} not found`);
              return;
            }

            const userData = userDoc.data()!;
            const fcmToken = userData.fcmToken;

            if (!fcmToken) {
              console.warn(`⚠️ No FCM token for user ${uid}`);
              return;
            }

            // Check notification preferences
            const notificationSettings = userData.notificationSettings || {};
            if (notificationSettings.messages === false) {
              console.log(`🔕 User ${uid} has disabled message notifications`);
              return;
            }

            // Prepare notification content
            let notificationBody = '';
            switch (message.type) {
              case 'text':
                notificationBody = message.content;
                break;
              case 'image':
                notificationBody = '📷 Photo';
                break;
              case 'video':
                notificationBody = '🎥 Video';
                break;
              case 'voice':
                notificationBody = '🎤 Voice message';
                break;
              case 'file':
                notificationBody = '📎 File';
                break;
              default:
                notificationBody = 'New message';
            }

            // Prepare notification payload
            const notificationPayload = {
              token: fcmToken,
              notification: {
                title: chatData.isGroup ? `${senderName} in ${chatData.name}` : senderName,
                body: notificationBody,
                imageUrl: message.type === 'image' ? message.mediaUrl : undefined,
              },
              data: {
                type: 'message',
                chatId: chatId,
                messageId: messageId,
                senderId: message.senderId,
                senderName: senderName,
                senderAvatar: senderAvatar,
                chatName: chatData.name || '',
                isGroup: chatData.isGroup ? 'true' : 'false',
                messageType: message.type,
                timestamp: message.timestamp?.toMillis()?.toString() || '',
              },
              android: {
                notification: {
                  channelId: 'messages',
                  priority: 'high' as const,
                  defaultSound: true,
                  icon: 'ic_notification',
                },
                data: {
                  click_action: 'FLUTTER_NOTIFICATION_CLICK',
                },
              },
              apns: {
                payload: {
                  aps: {
                    sound: 'message_sound.wav',
                    badge: 1,
                    category: 'MESSAGE_CATEGORY',
                  },
                },
              },
            };

            // Send the notification
            const response = await admin.messaging().send(notificationPayload);
            console.log(`✅ Notification sent to user ${uid}: ${response}`);

            // Track notification delivery
            await admin.firestore().collection('notification_logs').add({
              userId: uid,
              type: 'message',
              chatId: chatId,
              messageId: messageId,
              senderId: message.senderId,
              status: 'sent',
              response: response,
              timestamp: admin.firestore.FieldValue.serverTimestamp(),
            });

          } catch (error) {
            console.error(`❌ Failed to send notification to user ${uid}:`, error);
            
            // Log failed notification
            await admin.firestore().collection('notification_logs').add({
              userId: uid,
              type: 'message',
              chatId: chatId,
              messageId: messageId,
              senderId: message.senderId,
              status: 'failed',
              error: error.toString(),
              timestamp: admin.firestore.FieldValue.serverTimestamp(),
            });
          }
        });

      await Promise.all(notificationPromises);
      console.log(`✅ Processed notifications for message ${messageId}`);

    } catch (error) {
      console.error('❌ Error in sendMessageNotification:', error);
    }
  });

/**
 * Cloud Function to send social interaction notifications
 * Triggered when likes, comments, follows are created
 */
export const sendSocialNotification = functions.firestore
  .document('notifications/{notificationId}')
  .onCreate(async (snap, context) => {
    try {
      const notification = snap.data();
      const notificationId = context.params.notificationId;

      console.log(`📱 Processing social notification ${notificationId}`);

      // Get recipient's FCM token
      const userDoc = await admin.firestore()
        .collection('users')
        .doc(notification.userId)
        .get();

      if (!userDoc.exists) {
        console.error(`❌ User ${notification.userId} not found`);
        return;
      }

      const userData = userDoc.data()!;
      const fcmToken = userData.fcmToken;

      if (!fcmToken) {
        console.warn(`⚠️ No FCM token for user ${notification.userId}`);
        return;
      }

      // Check notification preferences
      const notificationSettings = userData.notificationSettings || {};
      const notificationType = notification.type;

      if (notificationSettings[notificationType] === false) {
        console.log(`🔕 User ${notification.userId} has disabled ${notificationType} notifications`);
        return;
      }

      // Get sender information
      const senderDoc = await admin.firestore()
        .collection('users')
        .doc(notification.fromUserId)
        .get();

      const senderData = senderDoc.exists ? senderDoc.data()! : {};
      const senderName = senderData.username || senderData.displayName || 'Someone';

      // Prepare notification content based on type
      let title = '';
      let body = '';

      switch (notificationType) {
        case 'like':
          title = 'New Like';
          body = `${senderName} liked your post`;
          break;
        case 'comment':
          title = 'New Comment';
          body = `${senderName} commented on your post`;
          break;
        case 'follow':
          title = 'New Follower';
          body = `${senderName} started following you`;
          break;
        case 'story_view':
          title = 'Story View';
          body = `${senderName} viewed your story`;
          break;
        default:
          title = 'New Notification';
          body = notification.message || 'You have a new notification';
      }

      // Send notification
      const notificationPayload = {
        token: fcmToken,
        notification: {
          title: title,
          body: body,
        },
        data: {
          type: notificationType,
          notificationId: notificationId,
          fromUserId: notification.fromUserId,
          fromUserName: senderName,
          postId: notification.postId || '',
          storyId: notification.storyId || '',
          timestamp: notification.createdAt?.toMillis()?.toString() || '',
        },
        android: {
          notification: {
            channelId: 'social',
            priority: 'default' as const,
            defaultSound: true,
          },
        },
        apns: {
          payload: {
            aps: {
              sound: 'default',
              badge: 1,
            },
          },
        },
      };

      const response = await admin.messaging().send(notificationPayload);
      console.log(`✅ Social notification sent: ${response}`);

      // Update notification as sent
      await snap.ref.update({
        sent: true,
        sentAt: admin.firestore.FieldValue.serverTimestamp(),
        fcmResponse: response,
      });

    } catch (error) {
      console.error('❌ Error in sendSocialNotification:', error);
    }
  });

/**
 * Cloud Function to handle call notifications
 * Triggered when a call is initiated
 */
export const sendCallNotification = functions.firestore
  .document('calls/{callId}')
  .onCreate(async (snap, context) => {
    try {
      const call = snap.data();
      const callId = context.params.callId;

      console.log(`📞 Processing call notification ${callId}`);

      // Get caller information
      const callerDoc = await admin.firestore()
        .collection('users')
        .doc(call.initiatorId)
        .get();

      const callerData = callerDoc.exists ? callerDoc.data()! : {};
      const callerName = callerData.username || callerData.displayName || 'Someone';

      // Send notification to all participants except caller
      const notificationPromises = call.participantIds
        .filter((uid: string) => uid !== call.initiatorId)
        .map(async (uid: string) => {
          try {
            const userDoc = await admin.firestore()
              .collection('users')
              .doc(uid)
              .get();

            if (!userDoc.exists) return;

            const userData = userDoc.data()!;
            const fcmToken = userData.fcmToken;

            if (!fcmToken) return;

            const callType = call.type === 'video' ? 'Video' : 'Voice';
            
            const notificationPayload = {
              token: fcmToken,
              notification: {
                title: `Incoming ${callType} Call`,
                body: `${callerName} is calling you`,
              },
              data: {
                type: 'call',
                callId: callId,
                callType: call.type,
                callerId: call.initiatorId,
                callerName: callerName,
                channelName: call.channelName || '',
              },
              android: {
                notification: {
                  channelId: 'calls',
                  priority: 'max' as const,
                  defaultSound: true,
                  category: 'call',
                },
              },
              apns: {
                payload: {
                  aps: {
                    sound: 'call_sound.wav',
                    badge: 1,
                    category: 'CALL_CATEGORY',
                  },
                },
              },
            };

            const response = await admin.messaging().send(notificationPayload);
            console.log(`✅ Call notification sent to user ${uid}: ${response}`);

          } catch (error) {
            console.error(`❌ Failed to send call notification to user ${uid}:`, error);
          }
        });

      await Promise.all(notificationPromises);

    } catch (error) {
      console.error('❌ Error in sendCallNotification:', error);
    }
  });

/**
 * Cloud Function to process notification requests
 * Triggered when a notification request is created
 */
export const processNotificationRequest = functions.firestore
  .document('notification_requests/{requestId}')
  .onCreate(async (snap, context) => {
    try {
      const request = snap.data();
      const requestId = context.params.requestId;

      console.log(`📨 Processing notification request ${requestId}`);

      const notificationPayload = {
        token: request.fcmToken,
        notification: {
          title: request.title,
          body: request.body,
        },
        data: {
          ...request.data,
          type: request.type,
          requestId: requestId,
        },
      };

      const response = await admin.messaging().send(notificationPayload);
      console.log(`✅ Notification request processed: ${response}`);

      // Update request status
      await snap.ref.update({
        status: 'sent',
        sentAt: admin.firestore.FieldValue.serverTimestamp(),
        response: response,
      });

    } catch (error) {
      console.error('❌ Error in processNotificationRequest:', error);

      // Update request status as failed
      await snap.ref.update({
        status: 'failed',
        error: error.toString(),
        failedAt: admin.firestore.FieldValue.serverTimestamp(),
      });
    }
  });

/**
 * Enhanced notification sender function
 */
async function sendEnhancedNotification(userId: string, notification: {
  type: string;
  title: string;
  body: string;
  data: Record<string, any>;
}) {
  try {
    // Get user's FCM token and notification preferences
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      console.log(`User ${userId} not found`);
      return;
    }

    const userData = userDoc.data()!;
    const fcmToken = userData.fcmToken;
    const notificationSettings = userData.notificationSettings || {};

    // Check if user has disabled this type of notification
    if (!fcmToken || notificationSettings[notification.type] === false) {
      console.log(`Notifications disabled for user ${userId} or type ${notification.type}`);
      return;
    }

    // Create FCM message
    const fcmMessage: admin.messaging.Message = {
      token: fcmToken,
      notification: {
        title: notification.title,
        body: notification.body,
      },
      data: {
        ...notification.data,
        type: notification.type,
        timestamp: Date.now().toString(),
      },
      android: {
        notification: {
          channelId: getNotificationChannel(notification.type),
          priority: getNotificationPriority(notification.type),
          sound: 'default',
          icon: getNotificationIcon(notification.type),
          color: '#D4AF37',
        },
        data: {
          click_action: 'FLUTTER_NOTIFICATION_CLICK',
        },
      },
      apns: {
        payload: {
          aps: {
            badge: 1,
            sound: 'default',
            category: notification.type.toUpperCase(),
          },
        },
      },
    };

    // Send notification
    await admin.messaging().send(fcmMessage);

    // Store notification in user's notification collection
    await db.collection('users').doc(userId).collection('notifications').add({
      type: notification.type,
      title: notification.title,
      body: notification.body,
      data: notification.data,
      isRead: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
    });

    // Update user's unread notification count
    await db.collection('users').doc(userId).update({
      unreadNotificationCount: admin.firestore.FieldValue.increment(1),
    });

    console.log(`📱 ${notification.type} notification sent to ${userId}`);
  } catch (error) {
    console.error(`❌ Error sending ${notification.type} notification to ${userId}:`, error);
  }
}

/**
 * Helper function to get notification channel based on type
 */
function getNotificationChannel(type: string): string {
  const channels: Record<string, string> = {
    follow: 'social',
    like: 'engagement',
    comment: 'engagement',
    share: 'engagement',
    mention: 'mentions',
    message: 'messages',
    call: 'calls',
  };
  return channels[type] || 'general';
}

/**
 * Helper function to get notification priority based on type
 */
function getNotificationPriority(type: string): 'min' | 'low' | 'default' | 'high' | 'max' {
  const priorities: Record<string, 'min' | 'low' | 'default' | 'high' | 'max'> = {
    follow: 'default',
    like: 'low',
    comment: 'default',
    share: 'default',
    mention: 'high',
    message: 'high',
    call: 'max',
  };
  return priorities[type] || 'default';
}

/**
 * Helper function to get notification icon based on type
 */
function getNotificationIcon(type: string): string {
  const icons: Record<string, string> = {
    follow: 'ic_person_add',
    like: 'ic_favorite',
    comment: 'ic_comment',
    share: 'ic_share',
    mention: 'ic_alternate_email',
    message: 'ic_message',
    call: 'ic_call',
  };
  return icons[type] || 'ic_notification';
}
