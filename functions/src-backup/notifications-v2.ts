import {onDocumentCreated, onDocumentDeleted} from 'firebase-functions/v2/firestore';
import {onCall, HttpsError} from 'firebase-functions/v2/https';
import {getFirestore, FieldValue} from 'firebase-admin/firestore';
import {getMessaging} from 'firebase-admin/messaging';
import {logger} from 'firebase-functions';

const db = getFirestore();

/**
 * Triggered when a user follows another user
 */
export const onFollowCreateV2 = onDocumentCreated('users/{userId}/followers/{followerId}', async (event) => {
  const snap = event.data;
  if (!snap) return;

  const userId = event.params.userId;
  const followerId = event.params.followerId;
  const followData = snap.data();

  logger.info(`👥 User ${followerId} followed user ${userId}`);

  try {
    // Update follower count
    await db.collection('users').doc(userId).update({
      followerCount: FieldValue.increment(1),
    });

    // Update following count
    await db.collection('users').doc(followerId).update({
      followingCount: FieldValue.increment(1),
    });

    // Send follow notification
    await sendFollowNotification(userId, followerId, 'follow');

    // Create notification record
    await createNotificationRecord(userId, followerId, 'follow', {
      type: 'follow',
      followedAt: followData.followedAt,
    });

    logger.info(`✅ Follow notification processed: ${followerId} -> ${userId}`);
  } catch (error) {
    logger.error('Error processing follow:', error);
  }
});

/**
 * Triggered when a user unfollows another user
 */
export const onFollowDeleteV2 = onDocumentDeleted('users/{userId}/followers/{followerId}', async (event) => {
  const userId = event.params.userId;
  const followerId = event.params.followerId;

  logger.info(`👥 User ${followerId} unfollowed user ${userId}`);

  try {
    // Update follower count
    await db.collection('users').doc(userId).update({
      followerCount: FieldValue.increment(-1),
    });

    // Update following count
    await db.collection('users').doc(followerId).update({
      followingCount: FieldValue.increment(-1),
    });

    logger.info(`✅ Unfollow processed: ${followerId} -> ${userId}`);
  } catch (error) {
    logger.error('Error processing unfollow:', error);
  }
});

/**
 * Triggered when a user likes a post
 */
export const onLikeCreateV2 = onDocumentCreated('posts/{postId}/likes/{userId}', async (event) => {
  const snap = event.data;
  if (!snap) return;

  const postId = event.params.postId;
  const userId = event.params.userId;
  const likeData = snap.data();

  logger.info(`❤️ User ${userId} liked post ${postId}`);

  try {
    // Get post data
    const postDoc = await db.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      logger.warn(`Post ${postId} not found`);
      return;
    }

    const postData = postDoc.data()!;
    const postOwnerId = postData.userId;

    // Don't notify if user liked their own post
    if (userId === postOwnerId) {
      return;
    }

    // Update post like count
    await db.collection('posts').doc(postId).update({
      likeCount: FieldValue.increment(1),
      engagementScore: FieldValue.increment(1),
    });

    // Send like notification
    await sendLikeNotification(postOwnerId, userId, postId, 'like');

    // Create notification record
    await createNotificationRecord(postOwnerId, userId, 'like', {
      type: 'like',
      postId: postId,
      likedAt: likeData.likedAt,
    });

    logger.info(`✅ Like notification processed: ${userId} -> post ${postId}`);
  } catch (error) {
    logger.error('Error processing like:', error);
  }
});

/**
 * Triggered when a user unlikes a post
 */
export const onLikeDeleteV2 = onDocumentDeleted('posts/{postId}/likes/{userId}', async (event) => {
  const postId = event.params.postId;
  const userId = event.params.userId;

  logger.info(`💔 User ${userId} unliked post ${postId}`);

  try {
    // Update post like count
    await db.collection('posts').doc(postId).update({
      likeCount: FieldValue.increment(-1),
      engagementScore: FieldValue.increment(-1),
    });

    logger.info(`✅ Unlike processed: ${userId} -> post ${postId}`);
  } catch (error) {
    logger.error('Error processing unlike:', error);
  }
});

/**
 * Triggered when a user comments on a post
 */
export const onCommentCreateV2 = onDocumentCreated('posts/{postId}/comments/{commentId}', async (event) => {
  const snap = event.data;
  if (!snap) return;

  const postId = event.params.postId;
  const commentId = event.params.commentId;
  const commentData = snap.data();

  logger.info(`💬 New comment ${commentId} on post ${postId}`);

  try {
    // Get post data
    const postDoc = await db.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      logger.warn(`Post ${postId} not found`);
      return;
    }

    const postData = postDoc.data()!;
    const postOwnerId = postData.userId;
    const commenterId = commentData.userId;

    // Update post comment count
    await db.collection('posts').doc(postId).update({
      commentCount: FieldValue.increment(1),
      engagementScore: FieldValue.increment(2), // Comments worth more than likes
    });

    // Don't notify if user commented on their own post
    if (commenterId === postOwnerId) {
      return;
    }

    // Send comment notification
    await sendCommentNotification(postOwnerId, commenterId, postId, commentId, commentData.content);

    // Create notification record
    await createNotificationRecord(postOwnerId, commenterId, 'comment', {
      type: 'comment',
      postId: postId,
      commentId: commentId,
      commentContent: commentData.content,
      commentedAt: commentData.createdAt,
    });

    logger.info(`✅ Comment notification processed: ${commenterId} -> post ${postId}`);
  } catch (error) {
    logger.error('Error processing comment:', error);
  }
});

/**
 * Triggered when a user shares a post
 */
export const onShareCreateV2 = onDocumentCreated('posts/{postId}/shares/{userId}', async (event) => {
  const snap = event.data;
  if (!snap) return;

  const postId = event.params.postId;
  const userId = event.params.userId;
  const shareData = snap.data();

  logger.info(`🔄 User ${userId} shared post ${postId}`);

  try {
    // Get post data
    const postDoc = await db.collection('posts').doc(postId).get();
    if (!postDoc.exists) {
      logger.warn(`Post ${postId} not found`);
      return;
    }

    const postData = postDoc.data()!;
    const postOwnerId = postData.userId;

    // Update post share count
    await db.collection('posts').doc(postId).update({
      shareCount: FieldValue.increment(1),
      engagementScore: FieldValue.increment(3), // Shares worth most
    });

    // Don't notify if user shared their own post
    if (userId === postOwnerId) {
      return;
    }

    // Send share notification
    await sendShareNotification(postOwnerId, userId, postId, 'share');

    // Create notification record
    await createNotificationRecord(postOwnerId, userId, 'share', {
      type: 'share',
      postId: postId,
      sharedAt: shareData.sharedAt,
    });

    logger.info(`✅ Share notification processed: ${userId} -> post ${postId}`);
  } catch (error) {
    logger.error('Error processing share:', error);
  }
});

/**
 * Helper function to send follow notification
 */
async function sendFollowNotification(userId: string, followerId: string, type: string) {
  try {
    const [userDoc, followerDoc] = await Promise.all([
      db.collection('users').doc(userId).get(),
      db.collection('users').doc(followerId).get(),
    ]);

    if (!userDoc.exists || !followerDoc.exists) return;

    const userData = userDoc.data()!;
    const followerData = followerDoc.data()!;
    const fcmToken = userData.fcmToken;

    if (!fcmToken) return;

    const fcmMessage = {
      token: fcmToken,
      notification: {
        title: 'New Follower',
        body: `${followerData.username || 'Someone'} started following you`,
      },
      data: {
        type: 'follow',
        followerId: followerId,
        followerUsername: followerData.username || '',
      },
    };

    await getMessaging().send(fcmMessage);
    logger.info(`📱 Follow notification sent to ${userId}`);
  } catch (error) {
    logger.error(`❌ Error sending follow notification:`, error);
  }
}

/**
 * Helper function to send like notification
 */
async function sendLikeNotification(userId: string, likerId: string, postId: string, type: string) {
  try {
    const [userDoc, likerDoc] = await Promise.all([
      db.collection('users').doc(userId).get(),
      db.collection('users').doc(likerId).get(),
    ]);

    if (!userDoc.exists || !likerDoc.exists) return;

    const userData = userDoc.data()!;
    const likerData = likerDoc.data()!;
    const fcmToken = userData.fcmToken;

    if (!fcmToken) return;

    const fcmMessage = {
      token: fcmToken,
      notification: {
        title: 'Post Liked',
        body: `${likerData.username || 'Someone'} liked your post`,
      },
      data: {
        type: 'like',
        likerId: likerId,
        postId: postId,
        likerUsername: likerData.username || '',
      },
    };

    await getMessaging().send(fcmMessage);
    logger.info(`📱 Like notification sent to ${userId}`);
  } catch (error) {
    logger.error(`❌ Error sending like notification:`, error);
  }
}

/**
 * Helper function to send comment notification
 */
async function sendCommentNotification(
  userId: string, 
  commenterId: string, 
  postId: string, 
  commentId: string, 
  commentContent: string
) {
  try {
    const [userDoc, commenterDoc] = await Promise.all([
      db.collection('users').doc(userId).get(),
      db.collection('users').doc(commenterId).get(),
    ]);

    if (!userDoc.exists || !commenterDoc.exists) return;

    const userData = userDoc.data()!;
    const commenterData = commenterDoc.data()!;
    const fcmToken = userData.fcmToken;

    if (!fcmToken) return;

    const fcmMessage = {
      token: fcmToken,
      notification: {
        title: 'New Comment',
        body: `${commenterData.username || 'Someone'} commented on your post`,
      },
      data: {
        type: 'comment',
        commenterId: commenterId,
        postId: postId,
        commentId: commentId,
        commenterUsername: commenterData.username || '',
      },
    };

    await getMessaging().send(fcmMessage);
    logger.info(`📱 Comment notification sent to ${userId}`);
  } catch (error) {
    logger.error(`❌ Error sending comment notification:`, error);
  }
}

/**
 * Helper function to send share notification
 */
async function sendShareNotification(userId: string, sharerId: string, postId: string, type: string) {
  try {
    const [userDoc, sharerDoc] = await Promise.all([
      db.collection('users').doc(userId).get(),
      db.collection('users').doc(sharerId).get(),
    ]);

    if (!userDoc.exists || !sharerDoc.exists) return;

    const userData = userDoc.data()!;
    const sharerData = sharerDoc.data()!;
    const fcmToken = userData.fcmToken;

    if (!fcmToken) return;

    const fcmMessage = {
      token: fcmToken,
      notification: {
        title: 'Post Shared',
        body: `${sharerData.username || 'Someone'} shared your post`,
      },
      data: {
        type: 'share',
        sharerId: sharerId,
        postId: postId,
        sharerUsername: sharerData.username || '',
      },
    };

    await getMessaging().send(fcmMessage);
    logger.info(`📱 Share notification sent to ${userId}`);
  } catch (error) {
    logger.error(`❌ Error sending share notification:`, error);
  }
}

/**
 * Helper function to create notification record
 */
async function createNotificationRecord(
  userId: string,
  fromUserId: string,
  type: string,
  metadata: any
) {
  try {
    await db.collection('users').doc(userId).collection('notifications').add({
      fromUserId: fromUserId,
      type: type,
      metadata: metadata,
      isRead: false,
      createdAt: FieldValue.serverTimestamp(),
    });

    // Update unread notification count
    await db.collection('users').doc(userId).update({
      unreadNotificationCount: FieldValue.increment(1),
    });
  } catch (error) {
    logger.error('Error creating notification record:', error);
  }
}
