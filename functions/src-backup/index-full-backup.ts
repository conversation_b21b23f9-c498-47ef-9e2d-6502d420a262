/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import {initializeApp} from "firebase-admin/app";
import {setGlobalOptions} from "firebase-functions";
import {onSchedule} from "firebase-functions/v2/scheduler";
import {logger} from "firebase-functions";
import {getFirestore} from "firebase-admin/firestore";
import {onPostLikeUpdate, onPostCommentUpdate, onPostShareUpdate} from "./engagementTriggers";

// Import test functions
export {
  createTestTrendingPost,
  testTrendingDetection,
  getTrendingPosts,
  resetTrendingStatus,
  addTestLike,
  addTestComment,
  addTestShare,
  getTrendingEvents,
  getTrendingPostsSimple,
} from "./testFunctions";

// Set global options for cost control
setGlobalOptions({maxInstances: 10});

// Initialize Firebase Admin
initializeApp();

const db = getFirestore();

// Trending config defaults
const DEFAULT_TRENDING_CONFIG = {
  minEngagementScore: 50,
  timeWindowHours: 3,
  maxTrendingPosts: 10,
  engagementWeights: {
    likes: 1,
    comments: 2,
    shares: 3,
  },
  timeDecayFactor: 24, // hours
  trendingThreshold: 50,
};

/**
 * Scheduled function to detect trending posts every hour
 */
export const scheduledTrendingDetection = onSchedule({
  schedule: "every 1 hours",
  region: "us-central1",
}, async () => {
  try {
    logger.info("Starting scheduled trending detection");
    await detectTrendingPosts();
    logger.info("Scheduled trending detection completed");
  } catch (error) {
    logger.error("Error in scheduled trending detection:", error);
  }
});

// Export engagement triggers
export {onPostLikeUpdate, onPostCommentUpdate, onPostShareUpdate};

// Export new backend functions
export * from './notifications';
export * from './messaging';
export * from './calls';
export * from './moderation';
export * from './maintenance';

/**
 * Helper function to get trending configuration
 */
async function getTrendingConfig() {
  try {
    const doc = await db.doc("settings/trending_config").get();
    if (doc.exists) {
      const config = doc.data();
      return {...DEFAULT_TRENDING_CONFIG, ...config};
    }
    return DEFAULT_TRENDING_CONFIG;
  } catch (error) {
    logger.error("Error fetching trending config:", error);
    return DEFAULT_TRENDING_CONFIG;
  }
}

/**
 * Main function to detect trending posts
 */
async function detectTrendingPosts(): Promise<void> {
  try {
    const config = await getTrendingConfig();
    const now = new Date();
    const windowStart = new Date(now.getTime() - config.timeWindowHours * 60 * 60 * 1000);

    // Query posts from last timeWindowHours
    const postsSnapshot = await db
      .collection("posts")
      .where("createdAt", ">=", windowStart)
      .orderBy("createdAt", "desc")
      .limit(100)
      .get();

    const batch = db.batch();
    let trendingCount = 0;

    for (const doc of postsSnapshot.docs) {
      const postData = doc.data();
      const postTime = postData.createdAt?.toDate() || now;
      const timeDiff = (now.getTime() - postTime.getTime()) / (1000 * 60 * 60);

      // Get engagement counts
      const [likesSnapshot, commentsSnapshot, sharesSnapshot] = await Promise.all([
        doc.ref.collection("likes").get(),
        doc.ref.collection("comments").get(),
        doc.ref.collection("shares").get(),
      ]);

      const likesCount = likesSnapshot.size;
      const commentsCount = commentsSnapshot.size;
      const sharesCount = sharesSnapshot.size;

      // Calculate trending score
      const engagementScore = likesCount + (commentsCount * 2) + (sharesCount * 3);
      const timeDecay = Math.max(0.1, 1 - timeDiff / config.timeDecayFactor);
      const trendingScore = Math.round(engagementScore * timeDecay);
      const isTrending = trendingScore >= config.trendingThreshold;

      // Update post with trending data
      batch.update(doc.ref, {
        trendingScore,
        lastTrendingUpdate: now,
        isTrending,
      });

      if (isTrending) {
        trendingCount++;

        // Log trending event
        await db.collection("trending_events").add({
          postId: doc.id,
          trendingScore,
          engagementScore,
          timeDecay,
          likesCount,
          commentsCount,
          sharesCount,
          timestamp: now,
          eventType: "scheduled_detection",
        });
      }
    }

    await batch.commit();
    logger.info(`Trending detection complete. ${trendingCount} trending posts found.`);
  } catch (error) {
    logger.error("Error in detectTrendingPosts:", error);
    throw error;
  }
}

/**
 * Helper function to update trending configuration
 */
export async function updateTrendingConfig(newConfig: any): Promise<void> {
  try {
    await db.doc("settings/trending_config").set(newConfig, {merge: true});
    logger.info("Trending configuration updated successfully");
  } catch (error) {
    logger.error("Error updating trending config:", error);
    throw error;
  }
}

/**
 * Helper function to get current trending posts
 */
export async function getCurrentTrendingPosts(): Promise<any[]> {
  try {
    const snapshot = await db
      .collection("posts")
      .where("isTrending", "==", true)
      .orderBy("trendingScore", "desc")
      .limit(10)
      .get();

    return snapshot.docs.map((doc) => ({
      id: doc.id,
      ...doc.data(),
    }));
  } catch (error) {
    logger.error("Error fetching trending posts:", error);
    return [];
  }
}

/**
 * Helper function to reset trending status for all posts
 */
export async function resetAllTrendingStatus(): Promise<void> {
  try {
    const snapshot = await db
      .collection("posts")
      .where("isTrending", "==", true)
      .get();

    const batch = db.batch();
    snapshot.docs.forEach((doc) => {
      batch.update(doc.ref, {
        isTrending: false,
        trendingScore: 0,
        lastTrendingUpdate: null,
      });
    });

    await batch.commit();
    logger.info(`Reset trending status for ${snapshot.docs.length} posts`);
  } catch (error) {
    logger.error("Error resetting trending status:", error);
    throw error;
  }
}
