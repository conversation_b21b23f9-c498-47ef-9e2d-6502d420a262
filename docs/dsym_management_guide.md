# dSYM Management Guide for Firebase Crashlytics

## 🚨 Current Issue: 248 Unprocessed Crashes

Your Firebase Crashlytics dashboard shows **248 unprocessed crashes** that need dSYM files to be properly symbolicated. This guide will help you resolve this issue and prevent it in the future.

## 🔍 What are dSYM Files?

dSYM (Debug Symbol) files contain debugging information that allows Firebase Crashlytics to convert memory addresses in crash reports into readable function names, file names, and line numbers. Without them, crash reports show only memory addresses, making debugging nearly impossible.

## 🛠 Immediate Solution: Upload Existing dSYMs

### Step 1: Generate dSYM Files (if needed)

If you don't have recent dSYM files, generate them:

```bash
# Option 1: Flutter build (recommended)
flutter build ios --release

# Option 2: Using Xcode
# - Open ios/Runner.xcworkspace in Xcode
# - Select 'Any iOS Device' as target
# - Product → Archive
```

### Step 2: Upload Existing dSYMs

Run the automated upload script:

```bash
# From project root directory
./scripts/upload_existing_dsyms.sh
```

This script will:
- ✅ Find all dSYM files in your project
- ✅ Upload them to Firebase Crashlytics
- ✅ Process your 248 unprocessed crashes

### Step 3: Verify Upload

1. Wait 5-10 minutes after upload
2. Check your Firebase Crashlytics dashboard
3. The unprocessed crashes should now show proper stack traces

## 🔄 Automatic dSYM Upload Setup

To prevent this issue in the future, we've set up automatic dSYM upload:

### iOS Build Script Configuration

The automatic upload script is located at:
- `ios/scripts/upload_dsyms.sh`

This script automatically uploads dSYMs for:
- ✅ Release builds
- ✅ Profile builds
- ❌ Debug builds (skipped for performance)

### Manual Upload When Needed

If automatic upload fails or you need to upload manually:

```bash
# Navigate to iOS directory
cd ios

# Run the upload script manually
./Pods/FirebaseCrashlytics/upload-symbols \
    -gsp Runner/GoogleService-Info.plist \
    -p ios \
    path/to/your/Runner.app.dSYM
```

## 📋 Best Practices

### 1. Always Build in Release Mode for Production

```bash
# For App Store builds
flutter build ios --release

# For TestFlight builds
flutter build ios --release
```

### 2. Verify dSYM Generation

Check that dSYMs are generated in your build settings:
- Open `ios/Runner.xcworkspace` in Xcode
- Build Settings → Debug Information Format
- Should be set to "DWARF with dSYM File" for Release

### 3. Monitor Crashlytics Dashboard

Regularly check your Firebase Crashlytics dashboard for:
- ✅ New crashes are being symbolicated
- ❌ No "unprocessed crashes" warnings
- 📊 Crash-free user percentage

## 🚨 Troubleshooting

### Issue: "Upload script not found"

**Solution**: Ensure Firebase Crashlytics pod is installed:
```bash
cd ios
pod install
```

### Issue: "GoogleService-Info.plist not found"

**Solution**: Verify the file exists at `ios/Runner/GoogleService-Info.plist`

### Issue: "No dSYM files found"

**Solution**: 
1. Build your app in Release mode
2. Check Xcode build settings for dSYM generation
3. Look for `.dSYM` files in build directories

### Issue: Upload fails with permissions error

**Solution**: Make sure the upload script is executable:
```bash
chmod +x ios/scripts/upload_dsyms.sh
chmod +x scripts/upload_existing_dsyms.sh
```

## 📊 Monitoring and Maintenance

### Weekly Checklist

- [ ] Check Firebase Crashlytics for unprocessed crashes
- [ ] Verify crash-free user percentage
- [ ] Review top crashes and fix critical issues

### After Each Release

- [ ] Confirm dSYMs were uploaded automatically
- [ ] Check that new crashes are properly symbolicated
- [ ] Monitor crash rates for regressions

## 🎯 Expected Results

After following this guide:

1. **Immediate**: 248 unprocessed crashes will be symbolicated
2. **Future**: All new crashes will be automatically symbolicated
3. **Debugging**: You'll see readable stack traces with file names and line numbers
4. **Monitoring**: Better crash reporting and faster issue resolution

## 📞 Support

If you continue to have issues:

1. Check Firebase Crashlytics documentation
2. Verify your Firebase project configuration
3. Ensure your app is properly connected to Firebase
4. Contact Firebase support if needed

---

**Next Steps**: Run `./scripts/upload_existing_dsyms.sh` to resolve the current issue!
