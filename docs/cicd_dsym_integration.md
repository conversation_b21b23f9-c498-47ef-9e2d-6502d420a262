# CI/CD Firebase Crashlytics dSYM Integration Guide

## 🚀 **Overview**

This guide provides comprehensive instructions for integrating Firebase Crashlytics dSYM upload into various CI/CD platforms for automated crash symbolication.

## 🔧 **GitHub Actions Integration**

### **Complete Workflow Example**

```yaml
name: iOS Build and Deploy
on:
  push:
    branches: [main, release/*]
  pull_request:
    branches: [main]

jobs:
  ios-build:
    runs-on: macos-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Flutter
        uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
          channel: 'stable'

      - name: Install dependencies
        run: flutter pub get

      - name: Setup iOS dependencies
        run: |
          cd ios
          pod install

      - name: Build iOS Release
        run: flutter build ios --release --no-codesign

      - name: Upload dSYMs to Firebase Crashlytics
        env:
          CONFIGURATION: Release
          PROJECT_DIR: ${{ github.workspace }}/ios
          PODS_ROOT: ${{ github.workspace }}/ios/Pods
        run: |
          cd ios
          chmod +x scripts/upload_dsyms.sh
          ./scripts/upload_dsyms.sh

      - name: Archive dSYMs (for backup)
        uses: actions/upload-artifact@v4
        with:
          name: dsyms-${{ github.sha }}
          path: ios/build/**/*.dSYM
          retention-days: 30
```

### **Secrets Configuration**

Add these secrets to your GitHub repository:

```yaml
# Repository Settings → Secrets and variables → Actions
FIREBASE_SERVICE_ACCOUNT_KEY: # Firebase service account JSON
IOS_CERTIFICATE_P12: # iOS distribution certificate
IOS_PROVISIONING_PROFILE: # iOS provisioning profile
```

## 🔧 **GitLab CI Integration**

### **Complete Pipeline Example**

```yaml
# .gitlab-ci.yml
stages:
  - build
  - upload-dsyms
  - deploy

variables:
  FLUTTER_VERSION: "3.16.0"
  
before_script:
  - export PATH="$PATH:$HOME/.pub-cache/bin"

ios-build:
  stage: build
  image: macos-latest
  script:
    - flutter --version
    - flutter pub get
    - cd ios && pod install
    - flutter build ios --release --no-codesign
  artifacts:
    paths:
      - ios/build/
    expire_in: 1 hour

upload-dsyms:
  stage: upload-dsyms
  image: macos-latest
  dependencies:
    - ios-build
  script:
    - cd ios
    - export CONFIGURATION="Release"
    - export PROJECT_DIR="$(pwd)"
    - export PODS_ROOT="$(pwd)/Pods"
    - chmod +x scripts/upload_dsyms.sh
    - ./scripts/upload_dsyms.sh
  only:
    - main
    - release/*
```

## 🔧 **Bitrise Integration**

### **Workflow Configuration**

```yaml
# bitrise.yml
workflows:
  ios-release:
    steps:
      - activate-ssh-key@4: {}
      - git-clone@8: {}
      
      - flutter-installer@0:
          inputs:
            - version: 3.16.0
            
      - script@1:
          title: Install Flutter dependencies
          inputs:
            - content: flutter pub get
            
      - cocoapods-install@2:
          inputs:
            - source_root_path: ios
            
      - flutter-build@0:
          inputs:
            - platform: ios
            - ios_additional_params: --release --no-codesign
            
      - script@1:
          title: Upload dSYMs to Firebase Crashlytics
          inputs:
            - content: |
                #!/bin/bash
                set -e
                cd ios
                export CONFIGURATION="Release"
                export PROJECT_DIR="$(pwd)"
                export PODS_ROOT="$(pwd)/Pods"
                chmod +x scripts/upload_dsyms.sh
                ./scripts/upload_dsyms.sh
```

## 🔧 **Codemagic Integration**

### **codemagic.yaml Configuration**

```yaml
workflows:
  ios-workflow:
    name: iOS Workflow
    instance_type: mac_mini_m1
    max_build_duration: 60
    environment:
      flutter: stable
      xcode: latest
      cocoapods: default
    scripts:
      - name: Install dependencies
        script: |
          flutter packages pub get
          cd ios && pod install

      - name: Build iOS
        script: |
          flutter build ios --release --no-codesign

      - name: Upload dSYMs to Firebase Crashlytics
        script: |
          cd ios
          export CONFIGURATION="Release"
          export PROJECT_DIR="$(pwd)"
          export PODS_ROOT="$(pwd)/Pods"
          chmod +x scripts/upload_dsyms.sh
          ./scripts/upload_dsyms.sh

    artifacts:
      - ios/build/**/*.dSYM
```

## 🔧 **Azure DevOps Integration**

### **azure-pipelines.yml Configuration**

```yaml
trigger:
  branches:
    include:
      - main
      - release/*

pool:
  vmImage: 'macOS-latest'

variables:
  FLUTTER_VERSION: '3.16.0'

steps:
  - task: FlutterInstall@0
    inputs:
      channel: 'stable'
      version: '$(FLUTTER_VERSION)'

  - script: flutter pub get
    displayName: 'Install Flutter dependencies'

  - script: |
      cd ios
      pod install
    displayName: 'Install iOS dependencies'

  - script: flutter build ios --release --no-codesign
    displayName: 'Build iOS Release'

  - script: |
      cd ios
      export CONFIGURATION="Release"
      export PROJECT_DIR="$(pwd)"
      export PODS_ROOT="$(pwd)/Pods"
      chmod +x scripts/upload_dsyms.sh
      ./scripts/upload_dsyms.sh
    displayName: 'Upload dSYMs to Firebase Crashlytics'

  - task: PublishBuildArtifacts@1
    inputs:
      pathToPublish: 'ios/build'
      artifactName: 'ios-dsyms'
```

## 🔧 **Jenkins Integration**

### **Jenkinsfile Configuration**

```groovy
pipeline {
    agent {
        label 'macos'
    }
    
    environment {
        FLUTTER_HOME = '/usr/local/flutter'
        PATH = "${FLUTTER_HOME}/bin:${PATH}"
    }
    
    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }
        
        stage('Install Dependencies') {
            steps {
                sh 'flutter pub get'
                sh 'cd ios && pod install'
            }
        }
        
        stage('Build iOS') {
            steps {
                sh 'flutter build ios --release --no-codesign'
            }
        }
        
        stage('Upload dSYMs') {
            when {
                anyOf {
                    branch 'main'
                    branch 'release/*'
                }
            }
            steps {
                sh '''
                    cd ios
                    export CONFIGURATION="Release"
                    export PROJECT_DIR="$(pwd)"
                    export PODS_ROOT="$(pwd)/Pods"
                    chmod +x scripts/upload_dsyms.sh
                    ./scripts/upload_dsyms.sh
                '''
            }
        }
    }
    
    post {
        always {
            archiveArtifacts artifacts: 'ios/build/**/*.dSYM', allowEmptyArchive: true
        }
    }
}
```

## 🔧 **CircleCI Integration**

### **.circleci/config.yml Configuration**

```yaml
version: 2.1

orbs:
  flutter: circleci/flutter@2.0.0

jobs:
  build-ios:
    macos:
      xcode: 15.0.0
    steps:
      - checkout
      
      - flutter/install_sdk_and_pub:
          version: 3.16.0
          
      - run:
          name: Install iOS dependencies
          command: cd ios && pod install
          
      - run:
          name: Build iOS Release
          command: flutter build ios --release --no-codesign
          
      - run:
          name: Upload dSYMs to Firebase Crashlytics
          command: |
            cd ios
            export CONFIGURATION="Release"
            export PROJECT_DIR="$(pwd)"
            export PODS_ROOT="$(pwd)/Pods"
            chmod +x scripts/upload_dsyms.sh
            ./scripts/upload_dsyms.sh
            
      - store_artifacts:
          path: ios/build
          destination: dsyms

workflows:
  build-and-upload:
    jobs:
      - build-ios:
          filters:
            branches:
              only:
                - main
                - /release\/.*/
```

## 🛡️ **Security Best Practices**

### **Environment Variables**
```bash
# Required environment variables for CI/CD
CONFIGURATION=Release
PROJECT_DIR=/path/to/ios/project
PODS_ROOT=/path/to/ios/Pods

# Optional: Firebase service account (for advanced setups)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/service-account.json
```

### **Secrets Management**
- ✅ Store Firebase service account keys securely
- ✅ Use encrypted environment variables
- ✅ Rotate keys regularly
- ✅ Limit access to production secrets

## 📊 **Monitoring and Validation**

### **CI/CD Pipeline Validation**
```bash
# Add validation step to your pipeline
- name: Validate dSYM Upload
  run: |
    cd ios
    ./scripts/validate_dsym_setup.sh
```

### **Success Indicators**
- ✅ Build completes without errors
- ✅ dSYM upload logs show success
- ✅ Firebase Console shows uploaded dSYMs
- ✅ Crash reports are symbolicated

### **Failure Handling**
```bash
# Example error handling in CI/CD
upload_dsyms() {
    if ! ./scripts/upload_dsyms.sh; then
        echo "⚠️ dSYM upload failed, but continuing build"
        echo "Manual upload may be required"
        # Don't fail the build for dSYM upload issues
        return 0
    fi
}
```

## 🎯 **Best Practices**

### **Build Optimization**
- ✅ Only upload dSYMs for Release builds
- ✅ Cache dependencies between builds
- ✅ Parallelize build steps where possible
- ✅ Archive dSYMs as build artifacts

### **Error Handling**
- ✅ Don't fail builds for dSYM upload issues
- ✅ Log detailed error information
- ✅ Provide fallback manual upload instructions
- ✅ Monitor upload success rates

### **Performance**
- ✅ Upload dSYMs in parallel with other tasks
- ✅ Use build caching for faster iterations
- ✅ Only trigger on relevant branches
- ✅ Optimize pod installation

---

## 🚀 **Quick Start Templates**

Choose your CI/CD platform and copy the relevant configuration above. All templates include:

1. **✅ Flutter setup** and dependency installation
2. **✅ iOS build** with Release configuration
3. **✅ Automated dSYM upload** to Firebase Crashlytics
4. **✅ Error handling** and artifact archiving
5. **✅ Branch filtering** for production deployments

Your CI/CD pipeline will now automatically upload dSYMs for proper crash symbolication! 🎉

---

*CI/CD integration guide created: 2025-07-18*
*All major platforms supported*
*Production-ready configurations provided*
