# Firebase Crashlytics dSYM Upload Setup Guide

## 🎯 **Overview**

This guide provides complete setup instructions for Firebase Crashlytics dSYM upload to ensure proper crash symbolication in your iOS app.

## 📋 **Prerequisites**

- ✅ Firebase project configured
- ✅ GoogleService-Info.plist added to iOS project
- ✅ Firebase Crashlytics pod installed
- ✅ App successfully building and running

## 🔧 **Setup Methods**

### **Method 1: Manual Upload (Testing)**

For immediate testing and validation:

```bash
# Navigate to iOS directory
cd ios

# Manual upload command
./Pods/FirebaseCrashlytics/upload-symbols \
  -gsp ./Runner/GoogleService-Info.plist \
  -p ios \
  ./build/ios/Release-iphoneos/Runner.app.dSYM
```

### **Method 2: Automated Build Integration (Recommended)**

#### **Step 1: Add Build Phase in Xcode**

1. Open `ios/Runner.xcworkspace` in Xcode
2. Select the **Runner** target
3. Go to **Build Phases** tab
4. Click **+** → **New Run Script Phase**
5. Name it: **"Firebase Crashlytics dSYM Upload"**
6. Add this script:

```bash
# Firebase Crashlytics dSYM Upload
if [ "${CONFIGURATION}" = "Release" ] || [ "${CONFIGURATION}" = "Profile" ]; then
    echo "🔥 Uploading dSYMs to Firebase Crashlytics..."
    
    "${PODS_ROOT}/FirebaseCrashlytics/upload-symbols" \
        -gsp "${PROJECT_DIR}/Runner/GoogleService-Info.plist" \
        -p ios \
        "${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}"
    
    echo "✅ dSYM upload completed"
else
    echo "ℹ️ Debug build - skipping dSYM upload"
fi
```

#### **Step 2: Configure Build Phase**

- **Input Files**: `$(DWARF_DSYM_FOLDER_PATH)/$(DWARF_DSYM_FILE_NAME)/Contents/Resources/DWARF/$(TARGET_NAME)`
- **Output Files**: `$(DERIVED_FILE_DIR)/upload_symbols_output.txt`
- **Run script only when installing**: ✅ Checked

### **Method 3: Using Existing Script**

Your project already has a script at `ios/scripts/upload_dsyms.sh`. To use it:

```bash
# Make script executable
chmod +x ios/scripts/upload_dsyms.sh

# Add to Xcode Build Phase
/bin/sh "${PROJECT_DIR}/scripts/upload_dsyms.sh"
```

## 🔍 **Verification Steps**

### **1. Check dSYM Generation**
```bash
# After building, verify dSYM exists
ls -la ios/build/ios/Release-iphoneos/Runner.app.dSYM
```

### **2. Test Manual Upload**
```bash
# Test upload manually
cd ios
./Pods/FirebaseCrashlytics/upload-symbols \
  -gsp ./Runner/GoogleService-Info.plist \
  -p ios \
  ./build/ios/Release-iphoneos/Runner.app.dSYM
```

### **3. Verify in Firebase Console**
1. Go to Firebase Console → Crashlytics
2. Check **dSYMs** tab
3. Verify your app version appears with uploaded symbols

## 🚨 **Troubleshooting**

### **Common Issues**

#### **"upload-symbols not found"**
```bash
# Check if FirebaseCrashlytics pod is installed
ls ios/Pods/FirebaseCrashlytics/upload-symbols

# If missing, reinstall pods
cd ios && pod install
```

#### **"GoogleService-Info.plist not found"**
```bash
# Verify file exists
ls ios/Runner/GoogleService-Info.plist

# Check Xcode project includes the file
```

#### **"dSYM not found"**
```bash
# Ensure you're building Release configuration
flutter build ios --release

# Check dSYM location
find ios/build -name "*.dSYM" -type d
```

### **Build Configuration Issues**

#### **Debug vs Release**
- **Debug builds**: dSYM upload typically skipped
- **Release builds**: dSYM upload required
- **Profile builds**: dSYM upload recommended

#### **Archive vs Build**
- **flutter build ios**: Creates dSYM in build directory
- **Xcode Archive**: Creates dSYM in archive location
- **TestFlight/App Store**: Requires dSYM upload

## 📊 **Validation Commands**

### **Check Upload Script**
```bash
# Verify script exists and is executable
ls -la ios/Pods/FirebaseCrashlytics/upload-symbols
```

### **Test Configuration**
```bash
# Validate GoogleService-Info.plist
plutil -lint ios/Runner/GoogleService-Info.plist
```

### **Check dSYM Contents**
```bash
# List dSYM contents
dwarfdump --uuid ios/build/ios/Release-iphoneos/Runner.app.dSYM
```

## 🎯 **Best Practices**

### **Automated Upload**
- ✅ Add to build phases for automatic upload
- ✅ Only upload for Release/Profile builds
- ✅ Include error handling in scripts

### **Manual Upload**
- ✅ Use for testing and validation
- ✅ Upload immediately after release builds
- ✅ Verify upload success in Firebase Console

### **CI/CD Integration**
```bash
# Example CI/CD step
- name: Upload dSYMs to Crashlytics
  run: |
    cd ios
    ./Pods/FirebaseCrashlytics/upload-symbols \
      -gsp ./Runner/GoogleService-Info.plist \
      -p ios \
      ./build/ios/Release-iphoneos/Runner.app.dSYM
```

## ✅ **Success Indicators**

### **Upload Success**
- ✅ No error messages during upload
- ✅ dSYM appears in Firebase Console
- ✅ Crash reports show symbolicated stack traces

### **Symbolication Working**
- ✅ Crash reports show method names (not addresses)
- ✅ File names and line numbers visible
- ✅ Stack traces are readable and meaningful

---

## 🚀 **Quick Start**

For immediate setup:

1. **Test manual upload**:
   ```bash
   cd ios
   ./Pods/FirebaseCrashlytics/upload-symbols -gsp ./Runner/GoogleService-Info.plist -p ios ./build/ios/Release-iphoneos/Runner.app.dSYM
   ```

2. **Add to Xcode build phases** for automation

3. **Verify in Firebase Console** that dSYMs are uploaded

4. **Test crash reporting** to ensure symbolication works

Your Firebase Crashlytics dSYM upload is now properly configured! 🎉
