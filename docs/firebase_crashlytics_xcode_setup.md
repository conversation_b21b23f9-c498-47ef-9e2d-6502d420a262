# Firebase Crashlytics Xcode Configuration Guide

## 🎯 **Complete Step-by-Step Setup**

This guide provides the exact configuration steps to ensure Firebase Crashlytics dSYM files are automatically uploaded and crash reports are fully symbolicated.

---

## ✅ **1. Add Firebase Crashlytics Run Script**

### **Step-by-Step Instructions:**

1. **Open the project in Xcode**
   ```bash
   open ios/Runner.xcworkspace
   ```

2. **Select the Runner target**
   - In the project navigator, click on "Runner" (the blue project icon)
   - Ensure the "Runner" target is selected in the main panel

3. **Go to Build Phases**
   - Click on the "Build Phases" tab at the top

4. **Add New Run Script Phase**
   - Click the **+** button in the top-left
   - Choose **"New Run Script Phase"**

5. **Configure the Script**
   - **Name the script**: `Firebase Crashlytics`
   - **In the Script box**, paste exactly this:

   ```bash
   "${PODS_ROOT}/FirebaseCrashlytics/upload-symbols" \
     -gsp "${PROJECT_DIR}/Runner/GoogleService-Info.plist" \
     -p ios "${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}"
   ```

---

## ✅ **2. Add Required Input Files**

### **Still in the same Run Script, under "Input Files":**

Add these paths **one by one** (click + for each):

```
${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}
${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Resources/DWARF/${PRODUCT_NAME}
${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}/Contents/Info.plist
$(TARGET_BUILD_DIR)/$(UNLOCALIZED_RESOURCES_FOLDER_PATH)/GoogleService-Info.plist
$(TARGET_BUILD_DIR)/$(EXECUTABLE_PATH)
```

### **⚠️ Important Note:**
These paths are **required for Xcode 15+** to ensure proper dSYM detection and upload.

---

## ✅ **3. Verify Debug Information Format**

### **Step-by-Step Instructions:**

1. **Go to Build Settings**
   - Click on the "Build Settings" tab
   - Ensure the "Runner" target is selected

2. **Search for Debug Information Format**
   - In the search box, type: `Debug Information Format`

3. **Set the correct value**
   - Make sure it's set to: **`DWARF with dSYM File`**
   - This should be set for **ALL** build configurations:
     - ✅ **Debug**: `DWARF with dSYM File`
     - ✅ **Release**: `DWARF with dSYM File`
     - ✅ **Profile**: `DWARF with dSYM File`

---

## ✅ **4. Create a New Archive**

### **Once the above steps are done:**

1. **In Xcode, go to:**
   ```
   Product → Archive
   ```

2. **Wait for the Archive to finish**
   - This may take several minutes
   - Monitor the build progress

3. **Open Organizer**
   - Go to `Window → Organizer`
   - Click on "Archives" tab
   - Confirm the new archive appears

4. **Automatic Upload**
   - Firebase should now automatically upload dSYM files after each build
   - Check the build log for upload confirmation

---

## ✅ **5. If Automatic Upload Still Fails**

### **Manual Upload Options:**

#### **Option A: From Archive**
1. **Locate the Archive**
   - Open the archive → Show in Finder
   - Show Package Contents → locate the `dSYMs` folder

2. **Manual Upload via Firebase Console**
   - Go to Firebase Console → Crashlytics → dSYMs → Upload
   - Drag and drop the dSYM files

#### **Option B: Command Line Upload**
```bash
# Navigate to iOS directory
cd ios

# Manual upload command
./Pods/FirebaseCrashlytics/upload-symbols \
  -gsp Runner/GoogleService-Info.plist \
  -p ios ~/Library/Developer/Xcode/Archives/<DATE>/<YourApp>.xcarchive/dSYMs
```

#### **Option C: Use Our Manual Upload Script**
```bash
# Navigate to iOS directory
cd ios

# Run manual upload script
./scripts/manual_dsym_upload.sh

# Or with specific dSYM path
./scripts/manual_dsym_upload.sh /path/to/your.dSYM
```

---

## 🔍 **Verification Steps**

### **1. Check Build Phase Integration**
- ✅ "Firebase Crashlytics" appears in Build Phases
- ✅ Script content matches exactly
- ✅ Input files are all added
- ✅ No syntax errors in script

### **2. Verify Build Settings**
- ✅ Debug Information Format = "DWARF with dSYM File"
- ✅ Setting applied to all configurations
- ✅ No conflicting settings

### **3. Test Archive Process**
- ✅ Archive completes without errors
- ✅ dSYM files are generated
- ✅ Upload script executes during build
- ✅ Firebase Console shows uploaded dSYMs

### **4. Validate Symbolication**
- ✅ Trigger test crash using debug tools
- ✅ Wait 5-10 minutes for processing
- ✅ Check crash report shows method names
- ✅ Stack traces are readable (not memory addresses)

---

## 🚨 **Troubleshooting**

### **Common Issues and Solutions:**

#### **"upload-symbols not found"**
```bash
# Solution: Reinstall pods
cd ios
pod deintegrate
pod install
```

#### **"GoogleService-Info.plist not found"**
- Download fresh copy from Firebase Console
- Ensure it's in `ios/Runner/` directory
- Verify it's added to Xcode project

#### **"dSYM not found"**
- Ensure Debug Information Format is set correctly
- Build with Release configuration
- Check that dSYM files are generated in build output

#### **Build Phase Not Executing**
- Verify script is in correct build phase order
- Check that input files are properly configured
- Ensure no syntax errors in script

#### **Upload Succeeds but No Symbolication**
- Verify dSYM UUID matches crash report
- Check Firebase Console for processing status
- Wait up to 30 minutes for symbolication to complete

---

## 📊 **Expected Results**

### **After Successful Configuration:**

#### **Build Process**
- ✅ Archives complete without errors
- ✅ Build log shows dSYM upload messages
- ✅ No build failures from upload issues

#### **Firebase Console**
- ✅ dSYMs tab shows uploaded symbols
- ✅ App version listed with upload timestamp
- ✅ Processing status shows "Complete"

#### **Crash Reports**
- ✅ Stack traces show method names
- ✅ File names and line numbers visible
- ✅ Readable, meaningful crash information
- ✅ Debug vs production crashes distinguished

---

## 🎯 **Quick Validation Commands**

### **Check Configuration**
```bash
# Verify script exists
ls -la ios/Pods/FirebaseCrashlytics/upload-symbols

# Check GoogleService-Info.plist
ls -la ios/Runner/GoogleService-Info.plist

# Test manual upload
cd ios && ./scripts/manual_dsym_upload.sh --help
```

### **Build and Test**
```bash
# Clean build
flutter clean

# Build release
flutter build ios --release

# Create archive (in Xcode)
# Product → Archive
```

---

## 🚀 **Success Indicators**

Your Firebase Crashlytics configuration is working correctly when:

1. **✅ Build Phase Configured**: Script appears in Xcode Build Phases
2. **✅ Input Files Added**: All 5 required paths configured
3. **✅ Debug Format Set**: DWARF with dSYM File for all configurations
4. **✅ Archives Complete**: No errors during archive process
5. **✅ dSYMs Uploaded**: Firebase Console shows uploaded symbols
6. **✅ Crashes Symbolicated**: Readable stack traces in crash reports

**Your crash reporting system is now enterprise-grade!** 🎉

---

*Configuration guide created: 2025-07-18*
*Follows exact Firebase Crashlytics best practices*
*Compatible with Xcode 15+ requirements*
