# Drag Rotation Fix for Circular Story Layout

## Problem
The drag-to-rotate functionality in the circular story layout was not working properly. Users could not rotate the circular arrangement of stories by dragging.

## Root Cause Analysis

### 1. Gesture Conflict Issue
- **Problem**: Nested GestureDetectors were causing conflicts
- **Location**: Outer GestureDetector (drag rotation) vs Inner GestureDetector (story taps)
- **Impact**: Pan gestures were being consumed by inner detectors before reaching the rotation handler

### 2. Rotation Calculation Issue
- **Problem**: Absolute angle calculation instead of incremental rotation
- **Location**: `_onPanUpdate` method was setting `_rotation = angle` directly
- **Impact**: Stories would jump to absolute positions rather than rotating smoothly

## Solution Applied

### 1. Fixed Gesture Conflicts
**File**: `lib/features/stories/widgets/hybrid_story_carousel.dart`

```dart
// Added behavior to allow gestures to pass through
child: GestureDetector(
  behavior: HitTestBehavior.translucent, // Allow gestures to pass through
  onTap: () {
    // Handle story tap
  },
  child: AnimatedContainer(...)
)
```

### 2. Implemented Incremental Rotation
**Added tracking variable:**
```dart
// Track previous drag position for incremental rotation
Offset? _previousPanPosition;
```

**Updated pan start:**
```dart
void _onPanStart(DragStartDetails details) {
  _isDragging = true;
  _previousPanPosition = details.localPosition; // Track starting position
  _rotationController.stop();
}
```

**Fixed pan update with delta calculation:**
```dart
void _onPanUpdate(DragUpdateDetails details) {
  // Calculate angle delta from previous position to current position
  final currentPoint = details.localPosition;
  final previousPoint = _previousPanPosition!;
  
  // Calculate angles from center
  final currentAngle = math.atan2(currentPoint.dy - center.dy, currentPoint.dx - center.dx);
  final previousAngle = math.atan2(previousPoint.dy - center.dy, previousPoint.dx - center.dx);
  
  // Calculate angle delta and apply to rotation
  var angleDelta = currentAngle - previousAngle;
  
  // Handle angle wrap-around
  if (angleDelta > math.pi) {
    angleDelta -= 2 * math.pi;
  } else if (angleDelta < -math.pi) {
    angleDelta += 2 * math.pi;
  }

  setState(() {
    _rotation += angleDelta; // Incremental rotation
    _previousPanPosition = currentPoint;
  });
}
```

**Updated pan end:**
```dart
void _onPanEnd(DragEndDetails details) {
  _isDragging = false;
  _previousPanPosition = null; // Reset tracking
  _snapToNearestStory();
}
```

## Key Improvements

### 1. Smooth Rotation
- **Before**: Stories jumped to absolute angle positions
- **After**: Stories rotate smoothly based on drag movement

### 2. Gesture Handling
- **Before**: Gesture conflicts prevented rotation
- **After**: Both tap and drag gestures work correctly

### 3. Angle Wrap-around
- **Before**: No handling of angle transitions across 0°/360°
- **After**: Proper wrap-around handling for smooth rotation

### 4. Debug Logging
- Enhanced logging shows both rotation angle and delta for debugging
- Format: `🎯 Drag rotation: -127.7° (delta: -5.2°)`

## Expected Behavior

### User Interaction
1. **Drag Start**: User touches and starts dragging in circular area
2. **Drag Update**: Stories rotate smoothly following finger movement
3. **Drag End**: Stories snap to nearest story position with animation

### Visual Feedback
- Stories move in real-time during drag
- Smooth animation when snapping to final position
- Debug logs show rotation values for development

## Testing
To test the drag rotation:
1. Switch to circular layout mode
2. Touch and drag in the circular story area
3. Observe stories rotating smoothly
4. Release to see snap-to-nearest animation

## Files Modified
- `lib/features/stories/widgets/hybrid_story_carousel.dart`
  - Added `_previousPanPosition` tracking
  - Fixed gesture conflicts with `HitTestBehavior.translucent`
  - Implemented incremental rotation calculation
  - Added angle wrap-around handling
  - Enhanced debug logging

## Result
✅ Drag-to-rotate functionality now works smoothly
✅ No gesture conflicts between tap and drag
✅ Smooth incremental rotation based on finger movement
✅ Proper snap-to-nearest behavior on drag end
