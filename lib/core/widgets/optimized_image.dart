import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

class OptimizedImage extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final bool enableMemoryCache;
  final bool enableDiskCache;
  final Duration fadeInDuration;
  final String? cacheKey;
  final Map<String, String>? httpHeaders;
  final int? memCacheWidth;
  final int? memCacheHeight;

  const OptimizedImage({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.enableMemoryCache = true,
    this.enableDiskCache = true,
    this.fadeInDuration = const Duration(milliseconds: 300),
    this.cacheKey,
    this.httpHeaders,
    this.memCacheWidth,
    this.memCacheHeight,
  });

  @override
  Widget build(BuildContext context) {
    // Calculate optimal memory cache dimensions
    final devicePixelRatio = MediaQuery.of(context).devicePixelRatio;
    final optimalWidth =
        memCacheWidth ??
        (width != null ? (width! * devicePixelRatio).round() : null);
    final optimalHeight =
        memCacheHeight ??
        (height != null ? (height! * devicePixelRatio).round() : null);

    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      fadeInDuration: fadeInDuration,
      cacheKey: cacheKey,
      httpHeaders: httpHeaders,
      memCacheWidth: optimalWidth,
      memCacheHeight: optimalHeight,
      placeholder: (context, url) => placeholder ?? _buildDefaultPlaceholder(),
      errorWidget: (context, url, error) =>
          errorWidget ?? _buildDefaultErrorWidget(),
      imageBuilder: (context, imageProvider) =>
          _buildOptimizedImage(imageProvider),
    );
  }

  Widget _buildOptimizedImage(ImageProvider imageProvider) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        image: DecorationImage(image: imageProvider, fit: fit),
      ),
    );
  }

  Widget _buildDefaultPlaceholder() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[200],
      child: const Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
        ),
      ),
    );
  }

  Widget _buildDefaultErrorWidget() {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[300],
      child: const Icon(Icons.error_outline, color: Colors.grey, size: 32),
    );
  }
}

class OptimizedAvatarImage extends StatelessWidget {
  final String imageUrl;
  final double radius;
  final Widget? placeholder;
  final Widget? errorWidget;
  final VoidCallback? onTap;

  const OptimizedAvatarImage({
    super.key,
    required this.imageUrl,
    this.radius = 20,
    this.placeholder,
    this.errorWidget,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final widget = CircleAvatar(
      radius: radius,
      backgroundColor: Colors.grey[200],
      child: ClipOval(
        child: OptimizedImage(
          imageUrl: imageUrl,
          width: radius * 2,
          height: radius * 2,
          fit: BoxFit.cover,
          memCacheWidth: (radius * 2 * MediaQuery.of(context).devicePixelRatio)
              .round(),
          memCacheHeight: (radius * 2 * MediaQuery.of(context).devicePixelRatio)
              .round(),
          placeholder: placeholder ?? _buildDefaultAvatarPlaceholder(),
          errorWidget: errorWidget ?? _buildDefaultAvatarError(),
        ),
      ),
    );

    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: widget);
    }

    return widget;
  }

  Widget _buildDefaultAvatarPlaceholder() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      color: Colors.grey[300],
      child: Icon(Icons.person, size: radius, color: Colors.grey[600]),
    );
  }

  Widget _buildDefaultAvatarError() {
    return Container(
      width: radius * 2,
      height: radius * 2,
      color: Colors.grey[400],
      child: Icon(Icons.person, size: radius, color: Colors.white),
    );
  }
}

class OptimizedThumbnailImage extends StatelessWidget {
  final String imageUrl;
  final double size;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;

  const OptimizedThumbnailImage({
    super.key,
    required this.imageUrl,
    this.size = 60,
    this.onTap,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final widget = ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(8),
      child: OptimizedImage(
        imageUrl: imageUrl,
        width: size,
        height: size,
        fit: BoxFit.cover,
        memCacheWidth: (size * MediaQuery.of(context).devicePixelRatio).round(),
        memCacheHeight: (size * MediaQuery.of(context).devicePixelRatio)
            .round(),
        placeholder: _buildThumbnailPlaceholder(),
        errorWidget: _buildThumbnailError(),
      ),
    );

    if (onTap != null) {
      return GestureDetector(onTap: onTap, child: widget);
    }

    return widget;
  }

  Widget _buildThumbnailPlaceholder() {
    return Container(
      width: size,
      height: size,
      color: Colors.grey[200],
      child: Icon(Icons.image, size: size * 0.4, color: Colors.grey[400]),
    );
  }

  Widget _buildThumbnailError() {
    return Container(
      width: size,
      height: size,
      color: Colors.grey[300],
      child: Icon(
        Icons.broken_image,
        size: size * 0.4,
        color: Colors.grey[600],
      ),
    );
  }
}

// Preload images for better performance
class ImagePreloader {
  static final Map<String, ImageProvider> _cache = {};
  static const int _maxCacheSize = 100; // Prevent unlimited growth

  static Future<void> preloadImage(
    String imageUrl,
    BuildContext context,
  ) async {
    // Validate URL before proceeding
    if (imageUrl.isEmpty ||
        (!imageUrl.startsWith('http://') && !imageUrl.startsWith('https://'))) {
      debugPrint('Invalid image URL for preloading: $imageUrl');
      return;
    }

    if (_cache.containsKey(imageUrl)) return;

    // Prevent memory leaks by limiting cache size
    if (_cache.length >= _maxCacheSize) {
      _evictOldestEntries();
    }

    try {
      final imageProvider = CachedNetworkImageProvider(imageUrl);
      await precacheImage(imageProvider, context);
      _cache[imageUrl] = imageProvider;
    } catch (e) {
      debugPrint('Failed to preload image: $imageUrl, error: $e');
    }
  }

  static void _evictOldestEntries() {
    // Remove oldest 20% of entries when cache is full
    final entriesToRemove = (_maxCacheSize * 0.2).round();
    final keys = _cache.keys.take(entriesToRemove).toList();
    for (final key in keys) {
      _cache.remove(key);
    }
    debugPrint('🧹 ImagePreloader: Evicted $entriesToRemove old entries');
  }

  static Future<void> preloadImages(
    List<String> imageUrls,
    BuildContext context,
  ) async {
    final futures = imageUrls.map((url) => preloadImage(url, context));
    await Future.wait(futures);
  }

  static void clearCache() {
    _cache.clear();
  }

  static int get cacheSize => _cache.length;
}
