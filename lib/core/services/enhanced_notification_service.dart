import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Enhanced notification service with real-time push notification delivery
class EnhancedNotificationService {
  static final EnhancedNotificationService _instance =
      EnhancedNotificationService._internal();
  factory EnhancedNotificationService() => _instance;
  EnhancedNotificationService._internal();

  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  bool _isInitialized = false;
  String? _fcmToken;

  // Notification callbacks
  Function(RemoteMessage)? onMessageReceived;
  Function(RemoteMessage)? onMessageOpenedApp;
  Function(String)? onTokenRefresh;

  /// Initialize the notification service
  Future<bool> initializeNotifications() async {
    if (_isInitialized) return true;

    try {
      // Request notification permissions
      final settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus != AuthorizationStatus.authorized) {
        if (kDebugMode) {
          debugPrint('⚠️ Notifications: Permission denied');
        }
        return false;
      }

      // Initialize local notifications
      await _initializeLocalNotifications();

      // Get FCM token
      _fcmToken = await _messaging.getToken();
      if (kDebugMode) {
        debugPrint('✅ Notifications: FCM Token obtained: $_fcmToken');
      }

      // Save token to user profile
      await _saveFCMTokenToProfile();

      // Set up message handlers
      _setupMessageHandlers();

      // Listen for token refresh
      _messaging.onTokenRefresh.listen((token) {
        _fcmToken = token;
        _saveFCMTokenToProfile();
        onTokenRefresh?.call(token);
        if (kDebugMode) {
          debugPrint('🔄 Notifications: Token refreshed: $token');
        }
      });

      _isInitialized = true;
      if (kDebugMode) {
        debugPrint('✅ Notifications: Service initialized successfully');
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Notifications: Failed to initialize: $e');
      }
      return false;
    }
  }

  /// Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Create notification channels for Android
    await _createNotificationChannels();
  }

  /// Create notification channels for different types
  Future<void> _createNotificationChannels() async {
    const channels = [
      AndroidNotificationChannel(
        'messages',
        'Messages',
        description: 'Notifications for new messages',
        importance: Importance.high,
        sound: RawResourceAndroidNotificationSound('message_sound'),
      ),
      AndroidNotificationChannel(
        'social',
        'Social',
        description: 'Notifications for likes, comments, follows',
        importance: Importance.defaultImportance,
      ),
      AndroidNotificationChannel(
        'calls',
        'Calls',
        description: 'Notifications for incoming calls',
        importance: Importance.max,
        sound: RawResourceAndroidNotificationSound('call_sound'),
      ),
      AndroidNotificationChannel(
        'stories',
        'Stories',
        description: 'Notifications for story interactions',
        importance: Importance.defaultImportance,
      ),
    ];

    for (final channel in channels) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.createNotificationChannel(channel);
    }
  }

  /// Save FCM token to user profile
  Future<void> _saveFCMTokenToProfile() async {
    final currentUser = _auth.currentUser;
    if (currentUser == null || _fcmToken == null) return;

    try {
      await _firestore.collection('users').doc(currentUser.uid).update({
        'fcmToken': _fcmToken,
        'lastTokenUpdate': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        debugPrint('✅ Notifications: FCM token saved to profile');
      }
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Notifications: Failed to save FCM token: $e');
      }
    }
  }

  /// Set up message handlers
  void _setupMessageHandlers() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // Handle messages when app is opened from background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessageOpenedApp);

    // Handle messages when app is opened from terminated state
    _messaging.getInitialMessage().then((message) {
      if (message != null) {
        _handleMessageOpenedApp(message);
      }
    });
  }

  /// Handle foreground messages
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    if (kDebugMode) {
      debugPrint(
        '📱 Notifications: Foreground message received: ${message.notification?.title}',
      );
    }

    // Show local notification
    await _showLocalNotification(message);

    // Call custom handler
    onMessageReceived?.call(message);

    // Track notification received
    await _trackNotificationEvent('received', message);
  }

  /// Handle messages when app is opened
  Future<void> _handleMessageOpenedApp(RemoteMessage message) async {
    if (kDebugMode) {
      debugPrint(
        '📱 Notifications: App opened from notification: ${message.notification?.title}',
      );
    }

    // Call custom handler
    onMessageOpenedApp?.call(message);

    // Track notification opened
    await _trackNotificationEvent('opened', message);

    // Navigate based on notification type
    await _handleNotificationNavigation(message);
  }

  /// Show local notification
  Future<void> _showLocalNotification(RemoteMessage message) async {
    final notification = message.notification;
    final data = message.data;

    if (notification == null) return;

    final notificationType = data['type'] ?? 'general';
    final channelId = _getChannelId(notificationType);

    await _localNotifications.show(
      message.hashCode,
      notification.title,
      notification.body,
      NotificationDetails(
        android: AndroidNotificationDetails(
          channelId,
          _getChannelName(channelId),
          importance: _getImportance(notificationType),
          priority: Priority.high,
          showWhen: true,
          icon: '@mipmap/ic_launcher',
          // largeIcon: null, // TODO: Implement network image loading for notifications
          styleInformation: _getStyleInformation(notification, data),
        ),
        iOS: DarwinNotificationDetails(
          presentAlert: true,
          presentBadge: true,
          presentSound: true,
          sound: _getIOSSound(notificationType),
          attachments: data['imageUrl'] != null
              ? [DarwinNotificationAttachment(data['imageUrl']!)]
              : null,
        ),
      ),
      payload: jsonEncode(data),
    );
  }

  /// Get notification channel ID based on type
  String _getChannelId(String type) {
    switch (type) {
      case 'message':
        return 'messages';
      case 'call':
        return 'calls';
      case 'story':
        return 'stories';
      case 'like':
      case 'comment':
      case 'follow':
        return 'social';
      default:
        return 'social';
    }
  }

  /// Get channel name
  String _getChannelName(String channelId) {
    switch (channelId) {
      case 'messages':
        return 'Messages';
      case 'calls':
        return 'Calls';
      case 'stories':
        return 'Stories';
      case 'social':
        return 'Social';
      default:
        return 'General';
    }
  }

  /// Get importance level
  Importance _getImportance(String type) {
    switch (type) {
      case 'call':
        return Importance.max;
      case 'message':
        return Importance.high;
      default:
        return Importance.defaultImportance;
    }
  }

  /// Get iOS sound
  String? _getIOSSound(String type) {
    switch (type) {
      case 'message':
        return 'message_sound.wav';
      case 'call':
        return 'call_sound.wav';
      default:
        return null;
    }
  }

  /// Get notification style information
  StyleInformation _getStyleInformation(
    RemoteNotification notification,
    Map<String, dynamic> data,
  ) {
    final type = data['type'] ?? 'general';

    if (type == 'message') {
      return MessagingStyleInformation(
        Person(
          name: data['senderName'] ?? 'Unknown',
          // icon: null, // TODO: Implement network image loading for person icons
        ),
        conversationTitle: data['chatName'],
        groupConversation: data['isGroup'] == 'true',
        messages: [
          Message(
            notification.body ?? '',
            DateTime.now(),
            Person(name: data['senderName'] ?? 'Unknown'),
          ),
        ],
      );
    }

    return BigTextStyleInformation(
      notification.body ?? '',
      contentTitle: notification.title,
    );
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      final data = jsonDecode(response.payload!);
      final message = RemoteMessage(data: Map<String, String>.from(data));
      _handleMessageOpenedApp(message);
    }
  }

  /// Handle navigation based on notification type
  Future<void> _handleNotificationNavigation(RemoteMessage message) async {
    final data = message.data;
    final type = data['type'];

    // This would be handled by the main app navigation
    // For now, just log the navigation intent
    if (kDebugMode) {
      debugPrint(
        '🧭 Notifications: Navigation intent - Type: $type, Data: $data',
      );
    }
  }

  /// Track notification events for analytics
  Future<void> _trackNotificationEvent(
    String event,
    RemoteMessage message,
  ) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      await _firestore.collection('notification_analytics').add({
        'userId': currentUser.uid,
        'event': event,
        'notificationType': message.data['type'] ?? 'unknown',
        'messageId': message.messageId,
        'timestamp': FieldValue.serverTimestamp(),
        'data': message.data,
      });
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Notifications: Failed to track event: $e');
      }
    }
  }

  /// Send notification to specific user
  Future<bool> sendNotificationToUser({
    required String userId,
    required String title,
    required String body,
    required String type,
    Map<String, String>? data,
  }) async {
    try {
      // Get user's FCM token
      final userDoc = await _firestore.collection('users').doc(userId).get();
      final fcmToken = userDoc.data()?['fcmToken'] as String?;

      if (fcmToken == null) {
        if (kDebugMode) {
          debugPrint('⚠️ Notifications: No FCM token for user $userId');
        }
        return false;
      }

      // This would typically be done via a backend service
      // For now, we'll store the notification request in Firestore
      // and let a Cloud Function handle the actual sending
      await _firestore.collection('notification_requests').add({
        'targetUserId': userId,
        'fcmToken': fcmToken,
        'title': title,
        'body': body,
        'type': type,
        'data': data ?? {},
        'fromUserId': _auth
            .currentUser
            ?.uid, // Changed from 'senderId' to match security rules
        'timestamp': FieldValue.serverTimestamp(),
        'status': 'pending',
      });

      if (kDebugMode) {
        debugPrint(
          '✅ Notifications: Notification request created for user $userId',
        );
      }
      return true;
    } catch (e) {
      if (kDebugMode) {
        debugPrint('❌ Notifications: Failed to send notification: $e');
      }
      return false;
    }
  }

  /// Get current FCM token
  String? get fcmToken => _fcmToken;

  /// Check if notifications are enabled
  bool get isInitialized => _isInitialized;

  /// Dispose resources
  void dispose() {
    // Clean up resources if needed
    if (kDebugMode) {
      debugPrint('✅ Notifications: Service disposed');
    }
  }
}
