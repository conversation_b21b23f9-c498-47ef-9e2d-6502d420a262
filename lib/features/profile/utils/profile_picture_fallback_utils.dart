import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

/// Utility class for handling profile picture fallbacks and placeholders
class ProfilePictureFallbackUtils {
  /// Generate a consistent fallback avatar URL based on user ID
  static String generateFallbackAvatarUrl(String userId) {
    // Use a consistent seed based on user ID for reproducible avatars
    return 'https://i.pravatar.cc/150?u=$userId';
  }

  /// Generate a fallback avatar URL with custom size
  static String generateFallbackAvatarUrlWithSize(String userId, int size) {
    return 'https://i.pravatar.cc/$size?u=$userId';
  }

  /// Check if a URL is a valid image URL
  static bool isValidImageUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    return url.startsWith('http://') || url.startsWith('https://');
  }

  /// Check if a URL is a fallback/placeholder URL
  static bool isFallbackUrl(String? url) {
    if (url == null || url.isEmpty) return true;
    // cspell:disable-next-line
    return url.contains('i.pravatar.cc') || url.contains('placeholder');
  }

  /// Get the best available profile picture URL with fallback
  static String getBestProfilePictureUrl(
    String? profilePictureUrl,
    String userId,
  ) {
    if (isValidImageUrl(profilePictureUrl) &&
        !isFallbackUrl(profilePictureUrl)) {
      return profilePictureUrl!;
    }
    return generateFallbackAvatarUrl(userId);
  }

  /// Create a profile image provider with fallback handling
  static ImageProvider? createProfileImageProvider(
    String? profilePictureUrl,
    String userId,
  ) {
    final effectiveUrl = getBestProfilePictureUrl(profilePictureUrl, userId);
    // Double-check URL validity before creating provider
    if (isValidImageUrl(effectiveUrl)) {
      return CachedNetworkImageProvider(effectiveUrl);
    }
    return null; // Return null if no valid URL available
  }

  /// Create a CircleAvatar with proper fallback handling
  static Widget createProfileAvatar({
    required String? profilePictureUrl,
    required String userId,
    required double radius,
    Color? backgroundColor,
    Color? iconColor,
    double? iconSize,
  }) {
    final effectiveUrl = getBestProfilePictureUrl(profilePictureUrl, userId);
    final isUsingFallback = isFallbackUrl(profilePictureUrl);

    if (isUsingFallback) {
      debugPrint('🔄 Using fallback profile picture for user: $userId');
    }

    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Colors.grey.shade200,
      backgroundImage: CachedNetworkImageProvider(effectiveUrl),
      onBackgroundImageError: (exception, stackTrace) {
        // If even the fallback fails, we'll show the icon
        debugPrint('❌ Failed to load profile image: $effectiveUrl');
      },
      child:
          null, // Let the background image show, fallback to icon if it fails
    );
  }

  /// Create a profile avatar with error handling and icon fallback
  static Widget createProfileAvatarWithIconFallback({
    required String? profilePictureUrl,
    required String userId,
    required double radius,
    Color? backgroundColor,
    Color? iconColor,
    double? iconSize,
  }) {
    final effectiveUrl = getBestProfilePictureUrl(profilePictureUrl, userId);
    final hasValidImage =
        isValidImageUrl(profilePictureUrl) && !isFallbackUrl(profilePictureUrl);

    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Colors.grey.shade200,
      child: hasValidImage
          ? ClipOval(
              child: CachedNetworkImage(
                imageUrl: effectiveUrl,
                width: radius * 2,
                height: radius * 2,
                fit: BoxFit.cover,
                placeholder: (context, url) => Icon(
                  Icons.person,
                  size: iconSize ?? radius * 0.6,
                  color: iconColor ?? Colors.grey.shade600,
                ),
                errorWidget: (context, url, error) => Icon(
                  Icons.person,
                  size: iconSize ?? radius * 0.6,
                  color: iconColor ?? Colors.grey.shade600,
                ),
              ),
            )
          : Icon(
              Icons.person,
              size: iconSize ?? radius * 0.6,
              color: iconColor ?? Colors.grey.shade600,
            ),
    );
  }

  /// Create a story-aware profile avatar with border and fallback handling
  static Widget createStoryProfileAvatar({
    required String? profilePictureUrl,
    required String userId,
    required double radius,
    bool showBorder = false,
    Color? borderColor,
    double borderWidth = 2.0,
    Color? backgroundColor,
    Color? iconColor,
    double? iconSize,
  }) {
    final avatar = createProfileAvatarWithIconFallback(
      profilePictureUrl: profilePictureUrl,
      userId: userId,
      radius: showBorder ? radius - borderWidth : radius,
      backgroundColor: backgroundColor,
      iconColor: iconColor,
      iconSize: iconSize,
    );

    if (!showBorder) {
      return avatar;
    }

    return Container(
      width: radius * 2,
      height: radius * 2,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: Border.all(
          color: borderColor ?? Colors.purple,
          width: borderWidth,
        ),
      ),
      child: avatar,
    );
  }

  /// Get profile picture quality based on size requirements
  static String getOptimalProfilePictureUrl(
    String? profilePictureUrl,
    String userId,
    int targetSize,
  ) {
    if (isValidImageUrl(profilePictureUrl) &&
        !isFallbackUrl(profilePictureUrl)) {
      return profilePictureUrl!;
    }

    // Use appropriate size for fallback
    final size = _getOptimalFallbackSize(targetSize);
    return generateFallbackAvatarUrlWithSize(userId, size);
  }

  /// Get optimal fallback size based on target size
  static int _getOptimalFallbackSize(int targetSize) {
    // Round up to common sizes for better caching
    if (targetSize <= 50) return 50;
    if (targetSize <= 100) return 100;
    if (targetSize <= 150) return 150;
    if (targetSize <= 200) return 200;
    if (targetSize <= 300) return 300;
    return 400;
  }

  /// Create a loading placeholder for profile pictures
  static Widget createLoadingPlaceholder({
    required double radius,
    Color? backgroundColor,
  }) {
    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Colors.grey.shade200,
      child: SizedBox(
        width: radius,
        height: radius,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(Colors.grey.shade600),
        ),
      ),
    );
  }

  /// Create an error placeholder for profile pictures
  static Widget createErrorPlaceholder({
    required double radius,
    Color? backgroundColor,
    Color? iconColor,
    double? iconSize,
  }) {
    return CircleAvatar(
      radius: radius,
      backgroundColor: backgroundColor ?? Colors.grey.shade200,
      child: Icon(
        Icons.person,
        size: iconSize ?? radius * 0.6,
        color: iconColor ?? Colors.grey.shade600,
      ),
    );
  }

  /// Preload profile pictures for better performance
  static Future<void> preloadProfilePictures(
    BuildContext context,
    List<String> profilePictureUrls,
    List<String> userIds,
  ) async {
    final futures = <Future>[];

    for (int i = 0; i < profilePictureUrls.length && i < userIds.length; i++) {
      final effectiveUrl = getBestProfilePictureUrl(
        profilePictureUrls[i],
        userIds[i],
      );
      futures.add(
        precacheImage(
          CachedNetworkImageProvider(effectiveUrl),
          context,
        ).catchError((error) {
          debugPrint('❌ Failed to preload profile picture: $effectiveUrl');
        }),
      );
    }

    await Future.wait(futures);
    debugPrint('✅ Preloaded ${futures.length} profile pictures');
  }
}
