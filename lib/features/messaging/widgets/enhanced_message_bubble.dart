import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/messaging/models/chat_model.dart';
import 'package:intl/intl.dart';
import 'package:cached_network_image/cached_network_image.dart';

class EnhancedMessageBubble extends ConsumerStatefulWidget {
  final MessageModel message;
  final bool isMyMessage;
  final VoidCallback? onLongPress;
  final VoidCallback? onTap;
  final Function(String)? onReaction;
  final bool showTimestamp;
  final bool showStatus;

  const EnhancedMessageBubble({
    super.key,
    required this.message,
    required this.isMyMessage,
    this.onLongPress,
    this.onTap,
    this.onReaction,
    this.showTimestamp = true,
    this.showStatus = true,
  });

  @override
  ConsumerState<EnhancedMessageBubble> createState() =>
      _EnhancedMessageBubbleState();
}

class _EnhancedMessageBubbleState extends ConsumerState<EnhancedMessageBubble>
    with TickerProviderStateMixin {
  late AnimationController _bubbleAnimationController;
  late AnimationController _reactionAnimationController;
  late Animation<double> _bubbleScaleAnimation;
  late Animation<double> _bubbleOpacityAnimation;
  late Animation<Offset> _bubbleSlideAnimation;
  late Animation<double> _reactionScaleAnimation;

  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startBubbleAnimation();
  }

  void _initAnimations() {
    _bubbleAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _reactionAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _bubbleScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _bubbleAnimationController,
        curve: Curves.elasticOut,
      ),
    );

    _bubbleOpacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _bubbleAnimationController,
        curve: Curves.easeOut,
      ),
    );

    _bubbleSlideAnimation =
        Tween<Offset>(
          begin: widget.isMyMessage
              ? const Offset(1.0, 0.0)
              : const Offset(-1.0, 0.0),
          end: Offset.zero,
        ).animate(
          CurvedAnimation(
            parent: _bubbleAnimationController,
            curve: Curves.easeOutBack,
          ),
        );

    _reactionScaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _reactionAnimationController,
        curve: Curves.elasticOut,
      ),
    );
  }

  void _startBubbleAnimation() {
    _bubbleAnimationController.forward();
  }

  @override
  void dispose() {
    _bubbleAnimationController.dispose();
    _reactionAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _bubbleAnimationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _bubbleScaleAnimation.value,
          child: Opacity(
            opacity: _bubbleOpacityAnimation.value,
            child: SlideTransition(
              position: _bubbleSlideAnimation,
              child: _buildMessageContainer(),
            ),
          ),
        );
      },
    );
  }

  Widget _buildMessageContainer() {
    return Container(
      margin: EdgeInsets.only(
        left: widget.isMyMessage ? 48 : 8,
        right: widget.isMyMessage ? 8 : 48,
        bottom: 4,
      ),
      child: Column(
        crossAxisAlignment: widget.isMyMessage
            ? CrossAxisAlignment.end
            : CrossAxisAlignment.start,
        children: [
          // Message bubble
          GestureDetector(
            onTap: widget.onTap,
            onLongPress: widget.onLongPress,
            onTapDown: (_) => setState(() => _isPressed = true),
            onTapUp: (_) => setState(() => _isPressed = false),
            onTapCancel: () => setState(() => _isPressed = false),
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 150),
              transform: Matrix4.identity()..scale(_isPressed ? 0.95 : 1.0),
              child: _buildMessageBubble(),
            ),
          ),

          // Message metadata
          if (widget.showTimestamp || widget.showStatus)
            _buildMessageMetadata(),

          // Reactions
          if (widget.message.reactions.isNotEmpty) _buildReactions(),
        ],
      ),
    );
  }

  Widget _buildMessageBubble() {
    return Container(
      constraints: BoxConstraints(
        maxWidth: MediaQuery.of(context).size.width * 0.75,
        minWidth: 80,
      ),
      decoration: BoxDecoration(
        color: widget.isMyMessage
            ? AppTheme.accentColor
            : AppTheme.luxuryGrey.withValues(alpha: 0.8),
        borderRadius: BorderRadius.only(
          topLeft: const Radius.circular(20),
          topRight: const Radius.circular(20),
          bottomLeft: Radius.circular(widget.isMyMessage ? 20 : 4),
          bottomRight: Radius.circular(widget.isMyMessage ? 4 : 20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.only(
          topLeft: const Radius.circular(20),
          topRight: const Radius.circular(20),
          bottomLeft: Radius.circular(widget.isMyMessage ? 20 : 4),
          bottomRight: Radius.circular(widget.isMyMessage ? 4 : 20),
        ),
        child: _buildMessageContent(),
      ),
    );
  }

  Widget _buildMessageContent() {
    switch (widget.message.type) {
      case MessageType.text:
        return _buildTextContent();
      case MessageType.image:
        return _buildImageContent();
      case MessageType.video:
        return _buildVideoContent();
      case MessageType.file:
        return _buildFileContent();
      case MessageType.audio:
        return _buildAudioContent();
      case MessageType.location:
        return _buildLocationContent();
      case MessageType.contact:
        return _buildContactContent();
    }
  }

  Widget _buildTextContent() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Edited indicator
          if (widget.message.isEdited)
            Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Text(
                'edited',
                style: TextStyle(
                  fontSize: 11,
                  color: widget.isMyMessage
                      ? Colors.black.withValues(alpha: 0.6)
                      : Colors.white.withValues(alpha: 0.6),
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),

          // Message text
          SelectableText(
            widget.message.content,
            style: AppTheme.fontStyles.body.copyWith(
              color: widget.isMyMessage
                  ? AppTheme.luxuryBlack
                  : AppTheme.luxuryWhite,
              height: 1.4,
            ),
          ),

          // Self-destruct indicator
          if (widget.message.selfDestructSettings != null)
            _buildSelfDestructIndicator(),
        ],
      ),
    );
  }

  Widget _buildImageContent() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 250, maxHeight: 300),
      child: Stack(
        children: [
          // Image
          ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: const Radius.circular(20),
              topRight: const Radius.circular(20),
              bottomLeft: Radius.circular(widget.isMyMessage ? 20 : 4),
              bottomRight: Radius.circular(widget.isMyMessage ? 4 : 20),
            ),
            child:
                (widget.message.content.isNotEmpty &&
                    (widget.message.content.startsWith('http://') ||
                        widget.message.content.startsWith('https://')))
                ? CachedNetworkImage(
                    imageUrl: widget.message.content,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                      child: const Center(child: CircularProgressIndicator()),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[300],
                      child: const Icon(Icons.error),
                    ),
                  )
                : Container(
                    color: Colors.grey[300],
                    child: const Center(child: Icon(Icons.image_not_supported)),
                  ),
          ),

          // Play button overlay for videos
          if (widget.message.type == MessageType.video)
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.3),
                ),
                child: const Center(
                  child: Icon(
                    Icons.play_circle_fill,
                    color: Colors.white,
                    size: 48,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildVideoContent() {
    return _buildImageContent(); // Same as image for now
  }

  Widget _buildFileContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.insert_drive_file,
              color: AppTheme.accentColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Document',
                  style: AppTheme.fontStyles.body.copyWith(
                    color: widget.isMyMessage
                        ? AppTheme.luxuryBlack
                        : AppTheme.luxuryWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Tap to download',
                  style: TextStyle(
                    fontSize: 12,
                    color: widget.isMyMessage
                        ? Colors.black.withValues(alpha: 0.6)
                        : Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAudioContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.play_arrow, color: AppTheme.accentColor),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Voice Message',
                  style: AppTheme.fontStyles.body.copyWith(
                    color: widget.isMyMessage
                        ? AppTheme.luxuryBlack
                        : AppTheme.luxuryWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '0:30', // Duration placeholder
                  style: TextStyle(
                    fontSize: 12,
                    color: widget.isMyMessage
                        ? Colors.black.withValues(alpha: 0.6)
                        : Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.location_on, color: AppTheme.accentColor),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Location',
                  style: AppTheme.fontStyles.body.copyWith(
                    color: widget.isMyMessage
                        ? AppTheme.luxuryBlack
                        : AppTheme.luxuryWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Tap to view',
                  style: TextStyle(
                    fontSize: 12,
                    color: widget.isMyMessage
                        ? Colors.black.withValues(alpha: 0.6)
                        : Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.accentColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Icon(Icons.person, color: AppTheme.accentColor),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Contact',
                  style: AppTheme.fontStyles.body.copyWith(
                    color: widget.isMyMessage
                        ? AppTheme.luxuryBlack
                        : AppTheme.luxuryWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'Tap to view',
                  style: TextStyle(
                    fontSize: 12,
                    color: widget.isMyMessage
                        ? Colors.black.withValues(alpha: 0.6)
                        : Colors.white.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelfDestructIndicator() {
    return Container(
      margin: const EdgeInsets.only(top: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.timer, size: 12, color: Colors.red[400]),
          const SizedBox(width: 4),
          Text(
            'Self-destruct',
            style: TextStyle(
              fontSize: 10,
              color: Colors.red[400],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageMetadata() {
    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Timestamp
          if (widget.showTimestamp)
            Text(
              DateFormat('HH:mm').format(widget.message.timestamp),
              style: TextStyle(fontSize: 11, color: Colors.grey[500]),
            ),

          // Status indicators
          if (widget.showStatus && widget.isMyMessage) ...[
            const SizedBox(width: 4),
            _buildStatusIndicator(),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusIndicator() {
    IconData icon;
    Color color;

    switch (widget.message.status) {
      case MessageStatus.sending:
        icon = Icons.schedule;
        color = Colors.grey[400]!;
        break;
      case MessageStatus.sent:
        icon = Icons.check;
        color = Colors.grey[400]!;
        break;
      case MessageStatus.delivered:
        icon = Icons.done_all;
        color = Colors.grey[400]!;
        break;
      case MessageStatus.read:
        icon = Icons.done_all;
        color = AppTheme.accentColor;
        break;
      case MessageStatus.failed:
        icon = Icons.error;
        color = Colors.red[400]!;
        break;
    }

    return Icon(icon, size: 14, color: color);
  }

  Widget _buildReactions() {
    return Container(
      margin: const EdgeInsets.only(top: 4),
      child: Wrap(
        spacing: 4,
        children: widget.message.reactions.map((reaction) {
          return GestureDetector(
            onTap: () => widget.onReaction?.call(reaction.emoji),
            child: AnimatedBuilder(
              animation: _reactionAnimationController,
              builder: (context, child) {
                return Transform.scale(
                  scale: _reactionScaleAnimation.value,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.luxuryGrey.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      reaction.emoji,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                );
              },
            ),
          );
        }).toList(),
      ),
    );
  }
}
