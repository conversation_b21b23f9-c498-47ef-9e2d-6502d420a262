import 'package:billionaires_social/core/app_theme.dart';
import 'package:billionaires_social/features/stories/models/story_reel_model.dart';
import 'package:billionaires_social/features/profile/screens/user_profile_screen.dart';
import 'package:billionaires_social/features/profile/screens/main_profile_screen.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import 'package:get_it/get_it.dart';
import 'package:billionaires_social/core/service_locator.dart';
import 'package:billionaires_social/features/stories/services/story_service.dart';
import 'package:billionaires_social/features/stories/services/story_interaction_service.dart';
import 'package:billionaires_social/features/stories/models/story_interaction_model.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';

import '../widgets/story_analytics_display.dart';
import '../widgets/story_interactions_panel.dart';
import '../services/story_delete_service.dart';
import 'story_settings_screen.dart';
import 'dart:async';
import 'package:billionaires_social/core/utils/dialog_utils.dart';

class StoryViewerScreen extends ConsumerStatefulWidget {
  final StoryReel reel;
  final bool isOwnStory;
  final List<StoryReel>? allReels; // List of all reels for navigation
  final int? initialReelIndex; // Initial reel index in the list

  const StoryViewerScreen({
    super.key,
    required this.reel,
    this.isOwnStory = false,
    this.allReels,
    this.initialReelIndex,
  });

  @override
  ConsumerState<StoryViewerScreen> createState() => _StoryViewerScreenState();
}

class _StoryViewerScreenState extends ConsumerState<StoryViewerScreen>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late PageController _reelPageController; // For reel navigation
  late AnimationController _animationController;
  int _currentStoryIndex = 0;
  int _currentReelIndex = 0; // Current reel index
  late StoryReel _currentReel; // State variable to track current reel
  late List<StoryReel> _reels; // All reels for navigation
  bool _showAnalytics = false; // New: show analytics overlay

  bool _hasMarkedViewed = false; // New: track if marked as viewed
  Timer? _viewTimer; // New: timer for view tracking
  bool _isDeleting = false; // Track deletion state

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(vsync: this);

    // Initialize navigation system
    _reels = widget.allReels ?? [widget.reel];
    _currentReelIndex = widget.initialReelIndex ?? 0;
    _currentReel = _reels[_currentReelIndex];

    // Initialize reel page controller
    _reelPageController = PageController(initialPage: _currentReelIndex);

    _playStory(_currentReel.stories[_currentStoryIndex], markAsViewed: true);

    _animationController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _trackStoryCompletion();
        _nextStory();
      }
    });
  }

  @override
  void dispose() {
    // Track incomplete view if user exits before completion
    _trackIncompleteViewIfNeeded();

    _pageController.dispose();
    _reelPageController.dispose();
    _animationController.dispose();
    _viewTimer?.cancel();
    super.dispose();
  }

  void _trackStoryCompletion() {
    if (_hasMarkedViewed) {
      final currentStory = _currentReel.stories[_currentStoryIndex];
      final currentUserId = FirebaseAuth.instance.currentUser?.uid;

      if (currentUserId != null) {
        debugPrint('📊 Tracking story completion: ${currentStory.id}');
        try {
          // Check if service is available
          if (!getIt.isRegistered<StoryInteractionService>()) {
            debugPrint(
              '❌ StoryInteractionService not registered, skipping tracking',
            );
            return;
          }

          // Track completed view with full duration
          GetIt.I<StoryInteractionService>().trackStoryViewWithAnalytics(
            storyId: currentStory.id,
            viewerId: currentUserId,
            viewDuration: currentStory.duration,
            completed: true,
            deviceInfo: 'iOS', // Could be dynamic
            location: null, // Could be added if location permission is granted
          );
        } catch (e) {
          debugPrint('❌ Error tracking story completion: $e');
        }
      }
    } else {
      debugPrint('⚠️ Story completion not tracked - not marked as viewed');
    }
  }

  void _trackIncompleteViewIfNeeded() {
    if (_hasMarkedViewed && _animationController.isAnimating) {
      final currentStory = _currentReel.stories[_currentStoryIndex];
      final currentUserId = FirebaseAuth.instance.currentUser?.uid;

      if (currentUserId != null) {
        final viewDuration = Duration(
          milliseconds:
              (_animationController.value *
                      currentStory.duration.inMilliseconds)
                  .round(),
        );

        try {
          // Check if service is available
          if (!getIt.isRegistered<StoryInteractionService>()) {
            debugPrint(
              '❌ StoryInteractionService not registered, skipping tracking',
            );
            return;
          }

          // Track incomplete view
          GetIt.I<StoryInteractionService>().trackStoryViewWithAnalytics(
            storyId: currentStory.id,
            viewerId: currentUserId,
            viewDuration: viewDuration,
            completed: false,
            deviceInfo: 'iOS', // Could be dynamic
            location: null, // Could be added if location permission is granted
          );
        } catch (e) {
          debugPrint('❌ Error tracking incomplete view: $e');
        }
      }
    }
  }

  void _playStory(StoryItem story, {bool markAsViewed = false}) {
    debugPrint('🎬 Playing story: ${story.id} (markAsViewed: $markAsViewed)');
    _animationController.stop();
    _animationController.duration = story.duration;
    _animationController.forward(from: 0);
    _hasMarkedViewed = false;
    _viewTimer?.cancel();

    // Only mark as viewed after 1.5s
    if (markAsViewed) {
      debugPrint('🎬 Setting up view timer for story: ${story.id}');
      _viewTimer = Timer(const Duration(milliseconds: 1500), () {
        if (!_hasMarkedViewed) {
          debugPrint('🎬 Marking story as viewed: ${story.id}');
          GetIt.I<StoryService>().markSingleStoryAsViewed(story.id);
          ref.read(storyReelsProvider.notifier).refresh();
          _hasMarkedViewed = true;
          debugPrint('✅ Story marked as viewed: ${story.id}');
        }
      });
    }
  }

  void _nextStory() {
    if (_currentStoryIndex < _currentReel.stories.length - 1) {
      setState(() {
        _currentStoryIndex++;
      });
      _playStory(_currentReel.stories[_currentStoryIndex], markAsViewed: true);
    } else {
      // End of current reel, try to go to next reel
      _nextReel();
    }
  }

  void _navigateAfterDeletion() {
    // Check if there are remaining stories after deletion
    final remainingStories = _currentReel.stories
        .where(
          (story) => story.id != _currentReel.stories[_currentStoryIndex].id,
        )
        .toList();

    if (remainingStories.isNotEmpty) {
      // Update the reel with remaining stories
      setState(() {
        _currentReel = _currentReel.copyWith(stories: remainingStories);
        // Adjust current index if needed
        if (_currentStoryIndex >= remainingStories.length) {
          _currentStoryIndex = remainingStories.length - 1;
        }
      });
      // Play the next/current story
      _playStory(remainingStories[_currentStoryIndex], markAsViewed: true);
    } else {
      // No more stories, close viewer
      Navigator.of(context).pop();
    }
  }

  void _previousStory() {
    if (_currentStoryIndex > 0) {
      setState(() {
        _currentStoryIndex--;
      });
      _playStory(
        _currentReel.stories[_currentStoryIndex],
      ); // Don't re-mark as seen
    } else {
      // At the beginning of the reel, try to go to previous reel
      _previousReel();
    }
  }

  void _nextReel() {
    if (_currentReelIndex < _reels.length - 1) {
      setState(() {
        _currentReelIndex++;
        _currentReel = _reels[_currentReelIndex];
        _currentStoryIndex = 0;
      });
      _reelPageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _playStory(_currentReel.stories[_currentStoryIndex], markAsViewed: true);
    } else {
      // End of all reels, close the viewer
      Navigator.of(context).pop();
    }
  }

  void _previousReel() {
    if (_currentReelIndex > 0) {
      setState(() {
        _currentReelIndex--;
        _currentReel = _reels[_currentReelIndex];
        _currentStoryIndex =
            _currentReel.stories.length - 1; // Go to last story
      });
      _reelPageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      _playStory(_currentReel.stories[_currentStoryIndex], markAsViewed: true);
    } else {
      // At the beginning of all reels, close the viewer
      Navigator.of(context).pop();
    }
  }

  void _onTapDown(TapDownDetails details) {
    final screenWidth = MediaQuery.of(context).size.width;
    final dx = details.globalPosition.dx;

    if (dx < screenWidth * 0.3) {
      _previousStory();
    } else if (dx > screenWidth * 0.7) {
      _nextStory();
    } else {
      // Pause/resume logic
      if (_animationController.isAnimating) {
        _animationController.stop();
      } else {
        _animationController.forward();
      }
    }
  }

  // Pan gesture variables
  double _panStartY = 0;
  double _panStartX = 0;
  bool _isPanning = false;

  void _onPanUpdate(DragUpdateDetails details) {
    if (!_isPanning) {
      _panStartY = details.globalPosition.dy;
      _panStartX = details.globalPosition.dx;
      _isPanning = true;
    }
  }

  void _onPanEnd(DragEndDetails details) {
    if (!_isPanning) return;

    final deltaY = details.globalPosition.dy - _panStartY;
    final deltaX = details.globalPosition.dx - _panStartX;
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    final currentStory = _currentReel.stories[_currentStoryIndex];
    final isOwnStory = currentUserId == currentStory.userId;

    _isPanning = false;

    // Swipe up for insights (own stories only)
    if (deltaY < -100 && isOwnStory) {
      _openInsightsPanel();
    }
    // Swipe left for next reel
    else if (deltaX < -100) {
      _nextReel();
    }
    // Swipe right for previous reel
    else if (deltaX > 100) {
      _previousReel();
    }
  }

  void _openInsightsPanel() {
    final currentStory = _currentReel.stories[_currentStoryIndex];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StoryInteractionsPanel(
        storyId: currentStory.id,
        onClose: () {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final currentStory = _currentReel.stories[_currentStoryIndex];

    return GestureDetector(
      onTapDown: _onTapDown,
      onLongPress: () => _showContextMenu(currentStory),
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Stack(
          children: [
            // Media display
            Center(
              child:
                  currentStory.mediaUrl.isNotEmpty &&
                      (currentStory.mediaUrl.startsWith('http://') ||
                          currentStory.mediaUrl.startsWith('https://'))
                  ? CachedNetworkImage(
                      imageUrl: currentStory.mediaUrl,
                      fit: BoxFit.contain,
                      placeholder: (context, url) =>
                          AppTheme.loadingIndicator(),
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error, color: Colors.white),
                    )
                  : Container(
                      color: Colors.grey[900],
                      child: const Center(
                        child: Icon(
                          Icons.image_not_supported,
                          color: Colors.white,
                          size: 64,
                        ),
                      ),
                    ),
            ),

            // Story metadata overlays
            _buildStoryOverlays(currentStory),

            // UI overlay
            _buildUIOverlay(),
            // Analytics overlay
            if (_showAnalytics && widget.isOwnStory)
              Positioned.fill(
                child: Container(
                  color: Colors.black.withValues(alpha: 0.85),
                  child: Center(
                    child: StoryAnalyticsDisplay(
                      storyId: currentStory.id,
                      showDetailed: true,
                      onClose: () {
                        setState(() {
                          _showAnalytics = false;
                        });
                      },
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStoryOverlays(StoryItem story) {
    return Stack(
      children: [
        // Legacy text overlay (for backward compatibility)
        if (story.textOverlay != null && story.textOverlay!.isNotEmpty)
          Positioned(
            left: story.textPosition?['x'] ?? 100,
            top: story.textPosition?['y'] ?? 100,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                story.textOverlay!,
                style: TextStyle(
                  color: Color(story.textColor ?? 0xFFFFFFFF),
                  fontSize: story.textSize ?? 24.0,
                  fontWeight: FontWeight.bold,
                  shadows: [
                    Shadow(
                      offset: const Offset(1, 1),
                      blurRadius: 3,
                      color: Colors.black.withValues(alpha: 0.8),
                    ),
                  ],
                ),
              ),
            ),
          ),

        // Modern text elements (multiple text overlays)
        ...story.textElements.map((textElement) {
          return Positioned(
            left: textElement.position.dx,
            top: textElement.position.dy,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: textElement.backgroundColor,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                textElement.text,
                style: TextStyle(
                  color: textElement.color,
                  fontSize: textElement.size,
                  fontFamily: textElement.fontFamily,
                  fontWeight: textElement.fontWeight,
                  fontStyle: textElement.isItalic
                      ? FontStyle.italic
                      : FontStyle.normal,
                  shadows: [
                    Shadow(
                      offset: const Offset(1, 1),
                      blurRadius: 2,
                      color: Colors.black.withValues(alpha: 0.5),
                    ),
                  ],
                ),
                textAlign: textElement.textAlign,
              ),
            ),
          );
        }),

        // Tags overlay
        if (story.tags != null)
          ...story.tags!.map(
            (tag) => Positioned(
              left: tag.x * MediaQuery.of(context).size.width,
              top: tag.y * MediaQuery.of(context).size.height,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '@${tag.username.isNotEmpty ? tag.username : (tag.name ?? 'user')}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),

        // Music overlay
        if (story.music != null)
          Positioned(
            bottom: 100,
            right: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.music_note, color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    story.musicArtist ?? 'Music',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),

        // Location overlay
        if (story.location != null)
          Positioned(
            bottom: 100,
            left: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.location_on, color: Colors.white, size: 16),
                  const SizedBox(width: 8),
                  Text(
                    story.location!['name'] ?? 'Location',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildUIOverlay() {
    final currentStory = _currentReel.stories[_currentStoryIndex];
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    final isCurrentUserStory = currentUserId == currentStory.userId;

    return Stack(
      children: [
        // Progress bars and user info
        Positioned(
          top: 40,
          left: 10,
          right: 10,
          child: Column(
            children: [
              // Progress bars
              Row(
                children: _currentReel.stories.asMap().entries.map((entry) {
                  int index = entry.key;
                  return Expanded(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 2.0),
                      child: _ProgressBar(
                        animationController: _animationController,
                        isCurrent: index == _currentStoryIndex,
                        isCompleted: index < _currentStoryIndex,
                      ),
                    ),
                  );
                }).toList(),
              ),
              const SizedBox(height: 12),
              // User info and controls
              Row(
                children: [
                  // User profile section
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _navigateToUserProfile(_currentReel.userId),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircleAvatar(
                            radius: 16,
                            backgroundImage:
                                _currentReel.userAvatarUrl.isNotEmpty &&
                                    (_currentReel.userAvatarUrl.startsWith(
                                          'http://',
                                        ) ||
                                        _currentReel.userAvatarUrl.startsWith(
                                          'https://',
                                        ))
                                ? CachedNetworkImageProvider(
                                    _currentReel.userAvatarUrl,
                                  )
                                : null,
                            child:
                                (_currentReel.userAvatarUrl.isEmpty ||
                                    (!_currentReel.userAvatarUrl.startsWith(
                                          'http://',
                                        ) &&
                                        !_currentReel.userAvatarUrl.startsWith(
                                          'https://',
                                        )))
                                ? const Icon(
                                    Icons.person,
                                    color: Colors.white,
                                    size: 16,
                                  )
                                : null,
                          ),
                          const SizedBox(width: 8),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _currentReel.username,
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 1,
                                ),
                                Text(
                                  _formatTimeAgo(currentStory.timestamp),
                                  style: const TextStyle(
                                    color: Colors.white70,
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // Control buttons
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Settings button (only for own stories)
                      if (isCurrentUserStory) ...[
                        IconButton(
                          onPressed: _isDeleting
                              ? null
                              : () => _showStorySettings(currentStory),
                          icon: Icon(
                            Icons.settings,
                            color: _isDeleting ? Colors.grey : Colors.white,
                            size: 24,
                          ),
                          padding: const EdgeInsets.all(8),
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                        // Delete button (only for own stories)
                        IconButton(
                          onPressed: _isDeleting
                              ? null
                              : () => _showDeleteConfirmation(currentStory),
                          icon: _isDeleting
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white,
                                    ),
                                  ),
                                )
                              : const Icon(
                                  Icons.delete_outline,
                                  color: Colors.white,
                                  size: 24,
                                ),
                          padding: const EdgeInsets.all(8),
                          constraints: const BoxConstraints(
                            minWidth: 32,
                            minHeight: 32,
                          ),
                        ),
                      ],
                      // More options button
                      IconButton(
                        onPressed: () => _showMoreOptions(currentStory),
                        icon: const Icon(
                          Icons.more_vert,
                          color: Colors.white,
                          size: 24,
                        ),
                        padding: const EdgeInsets.all(8),
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                      ),
                      // Close button
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 24,
                        ),
                        padding: const EdgeInsets.all(8),
                        constraints: const BoxConstraints(
                          minWidth: 32,
                          minHeight: 32,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),
        // Bottom action bar for interactive features
        if (isCurrentUserStory)
          Positioned(
            bottom: 60,
            left: 20,
            right: 20,
            child: _buildBottomActionBar(currentStory),
          ),
        // Reaction and share buttons for viewing others' stories
        if (!isCurrentUserStory)
          Positioned(
            bottom: 60,
            right: 20,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildActionButton(
                  icon: Icons.favorite_border,
                  onTap: () => _showReactionSheet(currentStory),
                ),
                const SizedBox(height: 12),
                _buildActionButton(
                  icon: Icons.share,
                  onTap: () => _shareStory(currentStory),
                ),
                const SizedBox(height: 12),
                _buildActionButton(
                  icon: Icons.send,
                  onTap: () => _sendMessage(currentStory),
                ),
              ],
            ),
          ),
      ],
    );
  }

  void _navigateToUserProfile(String userId) {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    final isCurrentUser = currentUserId == userId;

    debugPrint(
      '🔍 Story Viewer Navigation: userId=$userId, currentUserId=$currentUserId, isCurrentUser=$isCurrentUser',
    );

    if (isCurrentUser) {
      // Navigate to current user's main profile (with edit options)
      debugPrint(
        '🏠 Navigating to current user main profile from story viewer',
      );
      Navigator.of(context).push(
        MaterialPageRoute(builder: (context) => const MainProfileScreen()),
      );
    } else {
      // Navigate to other user's profile (view only)
      debugPrint(
        '👤 Navigating to other user profile from story viewer: $userId',
      );
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => UserProfileScreen(userId: userId),
        ),
      );
    }
  }

  void _showReactionSheet(StoryItem story) {
    showAppDialog(
      context: context,
      title: Text('React to this story'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16),
          Wrap(
            alignment: WrapAlignment.center,
            spacing: 16,
            runSpacing: 12,
            children: [
              ...[
                {'emoji': '❤️', 'name': 'Love'},
                {'emoji': '🔥', 'name': 'Fire'},
                {'emoji': '👏', 'name': 'Clap'},
                {'emoji': '😍', 'name': 'Heart Eyes'},
                {'emoji': '😂', 'name': 'Laugh'},
                {'emoji': '😮', 'name': 'Wow'},
                {'emoji': '👍', 'name': 'Thumbs Up'},
                {'emoji': '👎', 'name': 'Thumbs Down'},
                {'emoji': '🎉', 'name': 'Party'},
                {'emoji': '💯', 'name': '100'},
                {'emoji': '✨', 'name': 'Sparkles'},
                {'emoji': '🚀', 'name': 'Rocket'},
              ].map((reaction) {
                return GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    _addReaction(story, reaction['emoji']!);
                  },
                  child: Column(
                    children: [
                      Text(
                        reaction['emoji']!,
                        style: const TextStyle(fontSize: 32),
                      ),
                      Text(
                        reaction['name']!,
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        ],
      ),
    );
  }

  void _addReaction(StoryItem story, String reaction) async {
    try {
      // Check if service is available
      if (!getIt.isRegistered<StoryInteractionService>()) {
        debugPrint('❌ StoryInteractionService not registered');
        return;
      }

      final interactionService = GetIt.instance<StoryInteractionService>();

      // Convert emoji to reaction type
      StoryReactionType reactionType;
      switch (reaction) {
        case '❤️':
          reactionType = StoryReactionType.love;
          break;
        case '😂':
          reactionType = StoryReactionType.haha;
          break;
        case '😮':
          reactionType = StoryReactionType.wow;
          break;
        case '😢':
          reactionType = StoryReactionType.sad;
          break;
        case '😡':
          reactionType = StoryReactionType.angry;
          break;
        default:
          reactionType = StoryReactionType.like;
      }

      await interactionService.addReaction(story.id, reactionType);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Reacted with $reaction'),
            duration: const Duration(seconds: 1),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add reaction: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareStory(StoryItem story) {
    showAppDialog(
      context: context,
      title: Text('Share to'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16),
          Wrap(
            alignment: WrapAlignment.center,
            spacing: 16,
            runSpacing: 12,
            children: [
              ...[
                {
                  'name': 'whatsapp',
                  'icon': Icons.chat,
                  'color': const Color(0xFF25D366),
                },
                {
                  'name': 'telegram',
                  'icon': Icons.send,
                  'color': const Color(0xFF0088CC),
                },
                {
                  'name': 'twitter',
                  'icon': Icons.flutter_dash,
                  'color': const Color(0xFF1DA1F2),
                },
                {
                  'name': 'facebook',
                  'icon': Icons.facebook,
                  'color': const Color(0xFF1877F2),
                },
                {
                  'name': 'instagram',
                  'icon': Icons.camera_alt,
                  'color': const Color(0xFFE4405F),
                },
                {
                  'name': 'email',
                  'icon': Icons.email,
                  'color': const Color(0xFFEA4335),
                },
                {
                  'name': 'sms',
                  'icon': Icons.sms,
                  'color': const Color(0xFF34A853),
                },
              ].map((platform) {
                final platformName = platform['name'] as String;
                final platformIcon = platform['icon'] as IconData;
                final platformColor = platform['color'] as Color;

                return GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    _shareToPlatform(story, platformName);
                  },
                  child: Column(
                    children: [
                      Container(
                        width: 50,
                        height: 50,
                        decoration: BoxDecoration(
                          color: platformColor,
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          platformIcon,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        platformName.toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
          const SizedBox(height: 16),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _copyStoryLink(story);
              },
              icon: const Icon(Icons.copy, size: 16),
              label: const Text('Copy Link'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Colors.white,
                side: const BorderSide(color: Colors.white),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _shareToPlatform(StoryItem story, String platform) async {
    try {
      final storyLink = 'https://billionaires-social.app/story/${story.id}';
      final shareText =
          'Check out this story from ${_currentReel.username} on Billionaires Social: $storyLink';

      switch (platform.toLowerCase()) {
        case 'instagram':
          // Copy link and show instruction
          await Clipboard.setData(ClipboardData(text: storyLink));
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Link copied! Open Instagram and paste in your story.',
                ),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );
          }
          break;
        case 'twitter':
          // Copy formatted tweet
          await Clipboard.setData(ClipboardData(text: shareText));
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Tweet text copied! Open Twitter to share.'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );
          }
          break;
        case 'whatsapp':
          // Copy message for WhatsApp
          await Clipboard.setData(ClipboardData(text: shareText));
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Message copied! Open WhatsApp to share.'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 3),
              ),
            );
          }
          break;
        default:
          // Generic sharing
          await Clipboard.setData(ClipboardData(text: storyLink));
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Sharing to $platform...'),
                backgroundColor: Colors.blue,
              ),
            );
          }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to share: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _copyStoryLink(StoryItem story) async {
    try {
      // Generate story link
      final storyLink = 'https://billionaires-social.app/story/${story.id}';

      // Copy to clipboard
      await Clipboard.setData(ClipboardData(text: storyLink));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Story link copied to clipboard!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to copy link: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Helper method to format time ago
  String _formatTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m';
    } else {
      return 'now';
    }
  }

  // Story settings method - connects to existing StorySettingsScreen
  void _showStorySettings(StoryItem story) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const StorySettingsScreen()),
    );
  }

  // Delete confirmation method
  void _showDeleteConfirmation(StoryItem story) {
    showAppDialog(
      context: context,
      title: const Text('Delete Story?', style: TextStyle(color: Colors.white)),
      content: const Text(
        'This story will be permanently deleted and cannot be recovered.',
        style: TextStyle(color: Colors.white70),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel', style: TextStyle(color: Colors.white70)),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            _deleteStory(story);
          },
          child: const Text('Delete', style: TextStyle(color: Colors.red)),
        ),
      ],
    );
  }

  // More options method - Instagram-style bottom sheet
  void _showMoreOptions(StoryItem story) {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    final isOwnStory = currentUserId == story.userId;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Color(0xFF1C1C1E),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            if (isOwnStory) ...[
              // Owner options
              _buildBottomSheetOption(
                icon: Icons.delete_outline,
                title: 'Delete Story',
                isDestructive: true,
                onTap: () {
                  Navigator.of(context).pop();
                  _showDeleteConfirmation(story);
                },
              ),
              _buildBottomSheetOption(
                icon: Icons.analytics_outlined,
                title: 'View Insights',
                onTap: () {
                  Navigator.of(context).pop();
                  _showStoryAnalytics(story);
                },
              ),
              _buildBottomSheetOption(
                icon: Icons.bookmark_add_outlined,
                title: 'Add to Highlights',
                onTap: () {
                  Navigator.of(context).pop();
                  _addToHighlights(story);
                },
              ),
              _buildBottomSheetOption(
                icon: Icons.edit_outlined,
                title: 'Edit Settings',
                onTap: () {
                  Navigator.of(context).pop();
                  _showStorySettings(story);
                },
              ),
            ] else ...[
              // Viewer options
              _buildBottomSheetOption(
                icon: Icons.volume_off_outlined,
                title: 'Mute ${_currentReel.username}',
                onTap: () {
                  Navigator.of(context).pop();
                  _muteUser(story);
                },
              ),
              _buildBottomSheetOption(
                icon: Icons.report_outlined,
                title: 'Report Story',
                isDestructive: true,
                onTap: () {
                  Navigator.of(context).pop();
                  _reportStory(story);
                },
              ),
              if (_isStoryPublic(story))
                _buildBottomSheetOption(
                  icon: Icons.link_outlined,
                  title: 'Copy Link',
                  onTap: () {
                    Navigator.of(context).pop();
                    _copyStoryLink(story);
                  },
                ),
            ],

            // Common options
            _buildBottomSheetOption(
              icon: Icons.share_outlined,
              title: 'Share Story',
              onTap: () {
                Navigator.of(context).pop();
                _shareStory(story);
              },
            ),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSheetOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? Colors.red : Colors.white,
        size: 24,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isDestructive ? Colors.red : Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 24, vertical: 4),
    );
  }

  bool _isStoryPublic(StoryItem story) {
    // Check if story is public based on privacy settings
    // This would integrate with your story visibility logic
    return true; // Simplified for now
  }

  // Bottom action bar for own stories
  Widget _buildBottomActionBar(StoryItem story) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Swipe up indicator
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.6),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.keyboard_arrow_up,
                color: Colors.white70,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                'Swipe up for insights',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),

        // Action buttons
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.black.withValues(alpha: 0.3),
            borderRadius: BorderRadius.circular(24),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                icon: Icons.person_add,
                label: 'Mention',
                onTap: () => _addMention(story),
              ),
              _buildActionButton(
                icon: Icons.music_note,
                label: 'Music',
                onTap: () => _addMusic(story),
              ),
              _buildActionButton(
                icon: Icons.location_on,
                label: 'Location',
                onTap: () => _addLocation(story),
              ),
              _buildActionButton(
                icon: Icons.poll,
                label: 'Poll',
                onTap: () => _addPoll(story),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Action button widget
  Widget _buildActionButton({
    required IconData icon,
    String? label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.6),
          shape: BoxShape.circle,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: Colors.white, size: 24),
            if (label != null) ...[
              const SizedBox(height: 4),
              Text(
                label,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // Send message method - Reply to story
  void _sendMessage(StoryItem story) {
    _showStoryReplyDialog(story);
  }

  void _showStoryReplyDialog(StoryItem story) {
    final TextEditingController messageController = TextEditingController();

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          decoration: const BoxDecoration(
            color: Color(0xFF1C1C1E),
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Handle bar
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: Colors.grey[600],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              const SizedBox(height: 20),

              // Header
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundImage:
                        (_currentReel.userAvatarUrl.isNotEmpty &&
                            (_currentReel.userAvatarUrl.startsWith('http://') ||
                                _currentReel.userAvatarUrl.startsWith(
                                  'https://',
                                )))
                        ? CachedNetworkImageProvider(_currentReel.userAvatarUrl)
                        : null,
                    child:
                        (_currentReel.userAvatarUrl.isEmpty ||
                            (!_currentReel.userAvatarUrl.startsWith(
                                  'http://',
                                ) &&
                                !_currentReel.userAvatarUrl.startsWith(
                                  'https://',
                                )))
                        ? const Icon(Icons.person, color: Colors.white)
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Reply to ${_currentReel.username}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          'This will be sent as a message',
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Message input
              Container(
                decoration: BoxDecoration(
                  color: Colors.grey[900],
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextField(
                        controller: messageController,
                        style: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          hintText: 'Send message...',
                          hintStyle: TextStyle(color: Colors.grey),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 20,
                            vertical: 12,
                          ),
                        ),
                        maxLines: 3,
                        minLines: 1,
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        if (messageController.text.trim().isNotEmpty) {
                          _sendStoryReply(story, messageController.text.trim());
                          Navigator.of(context).pop();
                        }
                      },
                      icon: const Icon(Icons.send, color: Colors.blue),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  void _sendStoryReply(StoryItem story, String message) async {
    try {
      // Check if service is available
      if (!getIt.isRegistered<StoryInteractionService>()) {
        debugPrint('❌ StoryInteractionService not registered');
        return;
      }

      final interactionService = GetIt.instance<StoryInteractionService>();
      await interactionService.addReply(story.id, message);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Reply sent!'),
            duration: Duration(seconds: 1),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send reply: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Add mention method
  void _addMention(StoryItem story) {
    // TODO: Navigate to mention selection screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add mention feature coming soon!')),
    );
  }

  // Story management methods
  Future<void> _deleteStory(StoryItem story) async {
    if (_isDeleting) return; // Prevent multiple deletions

    setState(() {
      _isDeleting = true;
    });

    try {
      // Show loading indicator
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 12),
              Text('Deleting story...'),
            ],
          ),
          duration: Duration(seconds: 2),
        ),
      );

      // Use the story delete service with ref for provider updates
      final success = await StoryDeleteService.deleteStory(
        context,
        story,
        ref: ref,
        onSuccess: () {
          debugPrint('[StoryViewer] Story deletion completed successfully');
        },
      );

      if (success) {
        // Navigate appropriately after successful deletion
        _navigateAfterDeletion();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to delete story: $e')));
      }
    } finally {
      setState(() {
        _isDeleting = false;
      });
    }
  }

  void _addToHighlights(StoryItem story) {
    // TODO: Show highlights selection
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add to highlights feature coming soon!')),
    );
  }

  void _muteUser(StoryItem story) async {
    try {
      // Show confirmation dialog
      final shouldMute = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          backgroundColor: const Color(0xFF1C1C1E),
          title: const Text('Mute User', style: TextStyle(color: Colors.white)),
          content: Text(
            'You won\'t see stories from ${_currentReel.username} anymore. You can unmute them from their profile.',
            style: const TextStyle(color: Colors.white70),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('Mute', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (shouldMute == true) {
        // Add user to muted list in Firebase
        final currentUserId = FirebaseAuth.instance.currentUser?.uid;
        if (currentUserId != null) {
          await FirebaseFirestore.instance
              .collection('users')
              .doc(currentUserId)
              .update({
                'mutedUsers': FieldValue.arrayUnion([_currentReel.userId]),
              });

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('${_currentReel.username} has been muted'),
                backgroundColor: Colors.green,
              ),
            );

            // Close story viewer since user is muted
            Navigator.of(context).pop();
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to mute user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _reportStory(StoryItem story) {
    _showReportDialog(story);
  }

  void _showReportDialog(StoryItem story) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Color(0xFF1C1C1E),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[600],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  const Text(
                    'Report Story',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Why are you reporting this story?',
                    style: TextStyle(color: Colors.grey[400], fontSize: 14),
                  ),
                ],
              ),
            ),

            // Report options
            ...[
              {
                'reason': StoryReportReason.inappropriate,
                'title': 'Inappropriate Content',
                'subtitle': 'Nudity, violence, or disturbing content',
              },
              {
                'reason': StoryReportReason.harassment,
                'title': 'Harassment or Bullying',
                'subtitle': 'Targeting someone with abuse',
              },
              {
                'reason': StoryReportReason.spam,
                'title': 'Spam',
                'subtitle': 'Repetitive or unwanted content',
              },
              {
                'reason': StoryReportReason.fakeNews,
                'title': 'False Information',
                'subtitle': 'Misleading or false content',
              },
              {
                'reason': StoryReportReason.violence,
                'title': 'Violence or Dangerous Organizations',
                'subtitle': 'Promoting violence or harmful groups',
              },
              {
                'reason': StoryReportReason.copyright,
                'title': 'Intellectual Property Violation',
                'subtitle': 'Copyright or trademark infringement',
              },
              {
                'reason': StoryReportReason.other,
                'title': 'Something Else',
                'subtitle': 'Other reason not listed above',
              },
            ].map((option) {
              return ListTile(
                title: Text(
                  option['title'] as String,
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
                subtitle: Text(
                  option['subtitle'] as String,
                  style: TextStyle(color: Colors.grey[400], fontSize: 12),
                ),
                onTap: () {
                  Navigator.of(context).pop();
                  _submitReport(story, option['reason'] as StoryReportReason);
                },
              );
            }),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _submitReport(StoryItem story, StoryReportReason reason) async {
    try {
      // Check if service is available
      if (!getIt.isRegistered<StoryInteractionService>()) {
        debugPrint('❌ StoryInteractionService not registered');
        return;
      }

      final interactionService = GetIt.instance<StoryInteractionService>();
      await interactionService.reportStory(story.id, reason, null);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'Story reported. Thank you for helping keep our community safe.',
            ),
            duration: Duration(seconds: 3),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to report story: $e'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Analytics method with ownership validation
  void _showStoryAnalytics(StoryItem story) {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;

    if (currentUserId != story.userId) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Row(
            children: [
              Icon(Icons.error_outline, color: Colors.white, size: 20),
              SizedBox(width: 12),
              Text('You can only view analytics for your own stories'),
            ],
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    setState(() {
      _showAnalytics = true;
    });
  }

  // Interactive feature methods
  void _addMusic(StoryItem story) {
    // TODO: Navigate to music selection
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add music feature coming soon!')),
    );
  }

  void _addLocation(StoryItem story) {
    // TODO: Navigate to location selection
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add location feature coming soon!')),
    );
  }

  void _addPoll(StoryItem story) {
    // TODO: Navigate to poll creation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Add poll feature coming soon!')),
    );
  }

  void _showContextMenu(StoryItem story) {
    final currentUserId = FirebaseAuth.instance.currentUser?.uid;
    final isOwnStory = currentUserId == story.userId;

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Color(0xFF1C1C1E),
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: Colors.grey[600],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundImage:
                        (_currentReel.userAvatarUrl.isNotEmpty &&
                            (_currentReel.userAvatarUrl.startsWith('http://') ||
                                _currentReel.userAvatarUrl.startsWith(
                                  'https://',
                                )))
                        ? CachedNetworkImageProvider(_currentReel.userAvatarUrl)
                        : null,
                    child:
                        (_currentReel.userAvatarUrl.isEmpty ||
                            (!_currentReel.userAvatarUrl.startsWith(
                                  'http://',
                                ) &&
                                !_currentReel.userAvatarUrl.startsWith(
                                  'https://',
                                )))
                        ? const Icon(Icons.person, color: Colors.white)
                        : null,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _currentReel.username,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          _formatTimeAgo(story.timestamp),
                          style: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Options
            if (isOwnStory) ...[
              _buildBottomSheetOption(
                icon: Icons.insights_outlined,
                title: 'View Insights',
                onTap: () {
                  Navigator.of(context).pop();
                  _openInsightsPanel();
                },
              ),
              _buildBottomSheetOption(
                icon: Icons.settings_outlined,
                title: 'Story Settings',
                onTap: () {
                  Navigator.of(context).pop();
                  _showStorySettings(story);
                },
              ),
              _buildBottomSheetOption(
                icon: Icons.delete_outline,
                title: 'Delete Story',
                isDestructive: true,
                onTap: () {
                  Navigator.of(context).pop();
                  _showDeleteConfirmation(story);
                },
              ),
            ] else ...[
              _buildBottomSheetOption(
                icon: Icons.chat_bubble_outline,
                title: 'Reply to Story',
                onTap: () {
                  Navigator.of(context).pop();
                  _sendMessage(story);
                },
              ),
              _buildBottomSheetOption(
                icon: Icons.share_outlined,
                title: 'Share Story',
                onTap: () {
                  Navigator.of(context).pop();
                  _shareStory(story);
                },
              ),
              _buildBottomSheetOption(
                icon: Icons.volume_off_outlined,
                title: 'Mute ${_currentReel.username}',
                onTap: () {
                  Navigator.of(context).pop();
                  _muteUser(story);
                },
              ),
              _buildBottomSheetOption(
                icon: Icons.report_outlined,
                title: 'Report Story',
                isDestructive: true,
                onTap: () {
                  Navigator.of(context).pop();
                  _reportStory(story);
                },
              ),
            ],

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}

class _ProgressBar extends StatelessWidget {
  final AnimationController animationController;
  final bool isCurrent;
  final bool isCompleted;

  const _ProgressBar({
    required this.animationController,
    required this.isCurrent,
    required this.isCompleted,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 3,
      decoration: BoxDecoration(
        color: isCompleted
            ? Colors.white
            : isCurrent
            ? Colors.grey[600]
            : Colors.grey[800],
        borderRadius: BorderRadius.circular(2),
      ),
      child: isCurrent
          ? AnimatedBuilder(
              animation: animationController,
              builder: (context, child) {
                return FractionallySizedBox(
                  widthFactor: animationController.value,
                  alignment: Alignment.centerLeft,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                );
              },
            )
          : const SizedBox.shrink(),
    );
  }
}
