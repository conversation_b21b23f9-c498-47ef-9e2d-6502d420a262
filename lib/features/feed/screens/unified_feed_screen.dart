import 'package:billionaires_social/features/feed/providers/feed_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_filter_provider.dart';
import 'package:billionaires_social/features/feed/providers/feed_scroll_controller_provider.dart';
import 'package:billionaires_social/features/feed/widgets/post_card.dart';
import 'package:billionaires_social/features/feed/widgets/post_card_skeleton.dart';
import 'package:billionaires_social/features/feed/widgets/category_selector.dart';
import 'package:billionaires_social/features/stories/widgets/dual_mode_story_carousel.dart';
import 'package:billionaires_social/features/stories/providers/story_provider.dart';
import 'package:billionaires_social/features/stories/screens/story_creation_screen.dart';
import 'package:billionaires_social/features/stories/screens/scalability_test_screen.dart';
import 'package:billionaires_social/features/auth/providers/auth_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';

import 'package:billionaires_social/core/instagram_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:billionaires_social/features/messaging/screens/chat_list_screen.dart';
import 'package:billionaires_social/features/notifications/screens/notifications_screen.dart';
import 'package:billionaires_social/features/explore/screens/search_screen.dart';
import 'package:billionaires_social/features/profile/screens/followers_following_screen.dart';
import 'package:billionaires_social/features/creation/screens/create_hub_screen.dart';

class UnifiedFeedScreen extends ConsumerStatefulWidget {
  const UnifiedFeedScreen({super.key});

  @override
  ConsumerState<UnifiedFeedScreen> createState() => _UnifiedFeedScreenState();
}

class _UnifiedFeedScreenState extends ConsumerState<UnifiedFeedScreen> {
  bool _useCircularLayout = true; // Default to circular layout
  ScrollController? _scrollController;

  @override
  void initState() {
    super.initState();
    // Initialize scroll controller from provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController = ref.read(feedScrollControllerProvider);
      _scrollController?.addListener(_onScroll);
    });
  }

  @override
  void dispose() {
    _scrollController?.removeListener(_onScroll);
    super.dispose();
  }

  void _onScroll() {
    final scrollController = _scrollController;
    if (scrollController != null && scrollController.hasClients) {
      // Check if we're near the bottom and should load more posts
      final maxScroll = scrollController.position.maxScrollExtent;
      final currentScroll = scrollController.position.pixels;
      final threshold = maxScroll * 0.8; // Load when 80% scrolled

      // Only trigger pagination if we're scrolling down and near the bottom
      if (currentScroll >= threshold &&
          !ref.read(feedProvider.notifier).isLoadingMore) {
        debugPrint('📄 Triggering pagination - loading more posts...');
        ref.read(feedProvider.notifier).fetchNextPage();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final feedState = ref.watch(feedProvider);
    final contentTypeFilter = ref.watch(
      contentTypeFilterProvider,
    ); // Watch content type filter
    final filterTitle = ref.read(feedFilterProvider.notifier).getFilterTitle();

    // Debug logging for filter state
    final currentFeedFilter = ref.watch(feedFilterProvider);
    debugPrint('🔍 UnifiedFeedScreen: contentTypeFilter = $contentTypeFilter');
    debugPrint('🔍 UnifiedFeedScreen: feedFilter = $currentFeedFilter');
    debugPrint('🔍 UnifiedFeedScreen: using feedProvider (not hybrid)');

    return Scaffold(
      appBar: AppBar(
        title: Text(filterTitle, style: Theme.of(context).textTheme.titleLarge),
        backgroundColor: Theme.of(context).appBarTheme.backgroundColor,
        foregroundColor: Theme.of(context).appBarTheme.foregroundColor,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (_) => const FollowersFollowingScreen(),
                ),
              );
            },
            icon: const FaIcon(FontAwesomeIcons.users),
          ),
          IconButton(
            onPressed: () {
              Navigator.of(
                context,
              ).push(MaterialPageRoute(builder: (_) => const SearchScreen()));
            },
            icon: const FaIcon(FontAwesomeIcons.magnifyingGlass),
          ),
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(builder: (_) => const NotificationsScreen()),
              );
            },
            icon: const FaIcon(FontAwesomeIcons.bell),
          ),
          IconButton(
            onPressed: () {
              Navigator.of(
                context,
              ).push(MaterialPageRoute(builder: (_) => const ChatListScreen()));
            },
            icon: const FaIcon(FontAwesomeIcons.paperPlane),
          ),
          IconButton(
            onPressed: () {
              showModalBottomSheet(
                context: context,
                backgroundColor: Colors.transparent,
                builder: (_) => const CreateHubScreen(),
              );
            },
            icon: const FaIcon(FontAwesomeIcons.plus),
          ),
          // Debug button for scalability test
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ScalabilityTestScreen(),
                ),
              );
            },
            icon: const Icon(Icons.speed, size: 20),
            tooltip: 'Test Story Scalability',
          ),
        ],
        elevation: 0,
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          // Trigger feed refresh
          debugPrint('🔄 Pull-to-refresh triggered');
          await ref.read(feedProvider.notifier).refresh();
        },
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        color: Theme.of(context).colorScheme.secondary,
        child: SafeArea(
          child: Consumer(
            builder: (context, ref, child) {
              final scrollController = ref.watch(feedScrollControllerProvider);
              return CustomScrollView(
                controller: scrollController,
                slivers: [
                  // Stories section with Instagram-style border
                  SliverToBoxAdapter(
                    child: Container(
                      decoration: const BoxDecoration(
                        color: InstagramTheme.white,
                        border: Border(
                          bottom: BorderSide(
                            color: InstagramTheme.borderGray,
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: Column(
                        children: [
                          // Stories section header with toggle
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  'Stories',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _useCircularLayout = !_useCircularLayout;
                                    });

                                    // Show feedback to user
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'Switched to ${_useCircularLayout ? "Circular" : "Traditional"} layout',
                                        ),
                                        duration: const Duration(seconds: 1),
                                        backgroundColor: const Color(
                                          0xFFD4AF37,
                                        ),
                                      ),
                                    );
                                  },
                                  child: AnimatedContainer(
                                    duration: const Duration(milliseconds: 300),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 6,
                                    ),
                                    decoration: BoxDecoration(
                                      color: _useCircularLayout
                                          ? const Color(0xFFD4AF37)
                                          : Colors.grey[300],
                                      borderRadius: BorderRadius.circular(16),
                                      border: Border.all(
                                        color: _useCircularLayout
                                            ? const Color(0xFFD4AF37)
                                            : Colors.grey[400]!,
                                        width: 2,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Icon(
                                          _useCircularLayout
                                              ? Icons.circle
                                              : Icons.view_carousel,
                                          size: 16,
                                          color: _useCircularLayout
                                              ? Colors.white
                                              : Colors.black,
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          _useCircularLayout
                                              ? 'Circular'
                                              : 'Traditional',
                                          style: TextStyle(
                                            color: _useCircularLayout
                                                ? Colors.white
                                                : Colors.black,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Hybrid story carousel
                          Consumer(
                            builder: (context, ref, child) {
                              final storyReelsAsync = ref.watch(
                                storyReelsProvider,
                              );

                              return storyReelsAsync.when(
                                data: (storyReels) {
                                  // Calculate height based on layout mode to prevent overflow
                                  final double carouselHeight =
                                      _useCircularLayout
                                      ? 390
                                      : 195; // Further increased traditional height to accommodate 155px + 32px + 8px

                                  return SizedBox(
                                    height: carouselHeight,
                                    child: DualModeStoryCarousel(
                                      key: ValueKey(
                                        'story_carousel_${storyReels.length}_$_useCircularLayout',
                                      ),
                                      storyReels: storyReels,
                                      useCircularLayout: _useCircularLayout,
                                      onCreateStory: () {
                                        // Navigate to story creation
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                const StoryCreationScreen(),
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                },
                                loading: () => const SizedBox(
                                  height: 100,
                                  child: Center(
                                    child: CircularProgressIndicator(),
                                  ),
                                ),
                                error: (error, stack) => SizedBox(
                                  height: 100,
                                  child: Center(
                                    child: Text(
                                      'Error loading stories: $error',
                                      style: const TextStyle(color: Colors.red),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SliverToBoxAdapter(child: CategorySelector()),
                  Consumer(
                    builder: (context, ref, child) {
                      final currentFilter = ref.watch(feedFilterProvider);

                      return feedState.when(
                        data: (posts) {
                          if (posts.isEmpty) {
                            // Show different empty states based on filter
                            if (currentFilter == FeedFilterType.followed) {
                              return SliverFillRemaining(
                                child: Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.people_outline,
                                        size: 64,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.6),
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        'No Posts in Your Feed',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium
                                            ?.copyWith(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onSurface
                                                  .withValues(alpha: 0.8),
                                              fontWeight: FontWeight.w600,
                                            ),
                                      ),
                                      const SizedBox(height: 8),
                                      Text(
                                        'Follow some users to see their posts here',
                                        style: Theme.of(context)
                                            .textTheme
                                            .bodyMedium
                                            ?.copyWith(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onSurface
                                                  .withValues(alpha: 0.6),
                                            ),
                                        textAlign: TextAlign.center,
                                      ),
                                      const SizedBox(height: 24),
                                      ElevatedButton.icon(
                                        onPressed: () {
                                          // Switch to all content filter for discovery
                                          ref
                                              .read(feedFilterProvider.notifier)
                                              .setFilter(FeedFilterType.all);
                                        },
                                        icon: const Icon(Icons.explore),
                                        label: const Text('Show All Content'),
                                      ),
                                      const SizedBox(height: 12),
                                      TextButton(
                                        onPressed: () {
                                          // Switch to explore tab to find users to follow
                                          DefaultTabController.of(
                                            context,
                                          ).animateTo(1);
                                        },
                                        child: const Text(
                                          'Find People to Follow',
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            } else {
                              return SliverFillRemaining(
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.post_add_outlined,
                                        size: 48, // Reduced size
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.6),
                                      ),
                                      const SizedBox(
                                        height: 12,
                                      ), // Reduced spacing
                                      Text(
                                        'No posts yet',
                                        style: Theme.of(context)
                                            .textTheme
                                            .titleMedium
                                            ?.copyWith(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .onSurface
                                                  .withValues(alpha: 0.8),
                                              fontWeight: FontWeight.w600,
                                            ),
                                      ),
                                      const SizedBox(
                                        height: 6,
                                      ), // Reduced spacing
                                      Flexible(
                                        child: Text(
                                          'Be the first to share something amazing!',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .onSurface
                                                    .withValues(alpha: 0.6),
                                              ),
                                          textAlign: TextAlign.center,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 16,
                                      ), // Reduced spacing
                                      ElevatedButton.icon(
                                        onPressed: () {
                                          showModalBottomSheet(
                                            context: context,
                                            backgroundColor: Colors.transparent,
                                            builder: (_) =>
                                                const CreateHubScreen(),
                                          );
                                        },
                                        icon: const Icon(Icons.add),
                                        label: const Text('Create Post'),
                                      ),
                                      const SizedBox(
                                        height: 8,
                                      ), // Reduced spacing
                                      TextButton(
                                        onPressed: () async {
                                          debugPrint(
                                            '🔄 Manual refresh button pressed',
                                          );
                                          await ref
                                              .read(feedProvider.notifier)
                                              .refresh();
                                        },
                                        child: const Text('Refresh'),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            }
                          }
                          return SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                // Show loading indicator at the end if loading more posts
                                if (index == posts.length) {
                                  return Consumer(
                                    builder: (context, ref, child) {
                                      final feedNotifier = ref.read(
                                        feedProvider.notifier,
                                      );
                                      final isLoadingMore =
                                          feedNotifier.isLoadingMore;
                                      final hasMore = feedNotifier.hasMore;

                                      debugPrint(
                                        '📄 Loading indicator: isLoadingMore=$isLoadingMore, hasMore=$hasMore',
                                      );

                                      if (isLoadingMore) {
                                        return Container(
                                          padding: const EdgeInsets.all(16),
                                          child: const Center(
                                            child: CircularProgressIndicator(),
                                          ),
                                        );
                                      } else if (!hasMore) {
                                        return Container(
                                          padding: const EdgeInsets.all(16),
                                          child: Center(
                                            child: Text(
                                              'You\'re all caught up!',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyMedium
                                                  ?.copyWith(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSurface
                                                        .withValues(alpha: 0.6),
                                                  ),
                                            ),
                                          ),
                                        );
                                      }
                                      return const SizedBox.shrink();
                                    },
                                  );
                                }
                                return PostCard(post: posts[index]);
                              },
                              childCount:
                                  posts.length + 1, // +1 for loading indicator
                            ),
                          );
                        },
                        error: (error, stackTrace) => SliverFillRemaining(
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.cloud_off_outlined,
                                  size: 64,
                                  color: Theme.of(context).colorScheme.error,
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'Connection Error',
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(
                                        color: Theme.of(
                                          context,
                                        ).colorScheme.error,
                                        fontWeight: FontWeight.w600,
                                      ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  error.toString().contains('not authenticated')
                                      ? 'Please sign in to view posts or check your connection for public content.'
                                      : 'Unable to load posts. Please check your connection.',
                                  style: Theme.of(context).textTheme.bodyMedium
                                      ?.copyWith(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onSurface
                                            .withValues(alpha: 0.7),
                                      ),
                                  textAlign: TextAlign.center,
                                ),
                                const SizedBox(height: 16),
                                // Debug info
                                if (kDebugMode) ...[
                                  Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: BoxDecoration(
                                      color: Colors.grey.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Column(
                                      children: [
                                        Text(
                                          'Debug Info:',
                                          style: Theme.of(
                                            context,
                                          ).textTheme.labelSmall,
                                        ),
                                        Text(
                                          'Auth: ${FirebaseAuth.instance.currentUser?.uid ?? "Not authenticated"}',
                                          style: Theme.of(
                                            context,
                                          ).textTheme.labelSmall,
                                        ),
                                        Text(
                                          'Error: ${error.toString()}',
                                          style: Theme.of(
                                            context,
                                          ).textTheme.labelSmall,
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 16),
                                ],
                                ElevatedButton.icon(
                                  onPressed: () async {
                                    debugPrint('🔄 Retry button pressed');
                                    debugPrint(
                                      '🔍 Current auth state: ${ref.read(authProvider).value}',
                                    );
                                    debugPrint(
                                      '🔍 Current user: ${FirebaseAuth.instance.currentUser?.uid}',
                                    );
                                    await ref
                                        .read(feedProvider.notifier)
                                        .refresh();
                                  },
                                  icon: const Icon(Icons.refresh),
                                  label: const Text('Try Again'),
                                ),
                              ],
                            ),
                          ),
                        ),
                        loading: () => SliverList(
                          delegate: SliverChildBuilderDelegate(
                            (context, index) => const PostCardSkeleton(),
                            childCount: 5,
                          ),
                        ),
                      );
                    },
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
